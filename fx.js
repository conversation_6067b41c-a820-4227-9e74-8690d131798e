import { LitElement } from '/fx/~/lit/3.2.1/lit-all.min.js';
export * from '/fx/~/lit/3.2.1/lit-all.min.js';

import { ulid, ulidr, decodeTime, decodeTimer, monotonicFactory } from '/fx/~/ulid/ulid.js';
import '/fx/~/icaro/icaro.js';

import '/fx/~/styles/adoptedStyleSheets.js'; // https://github.com/calebdwilliams/construct-style-sheets
import '/fx/~/styles/styles.js';

const urlFX = import.meta.url;

Object.defineProperty(Array.prototype, 'has', { enumerable: false, value: Array.prototype.includes })
Object.defineProperty(Array.prototype, 'clear', { enumerable: false, value: function () { this.splice(0) } })
Object.defineProperty(Array.prototype, 'first', { enumerable: false, get() { return this[0] } })
Object.defineProperty(Array.prototype, 'last', { enumerable: false, get() { return this[this.length - 1] } })
Object.defineProperty(Array.prototype, 'add', { enumerable: false, value: function (...item) { for (let i of item) { if (this.includes(i)) continue; this.push(i) } } })
Object.defineProperty(Array.prototype, 'remove', { enumerable: false, value: function (...items) { for (const item of items) { const idx = this.indexOf(item); if (idx < 0) continue; this.splice(idx, 1) } } })

export class FxElement extends LitElement {
    constructor() {
        super();

        this.$properties = this.constructor.elementProperties || this.constructor._classProperties;
        for (const k of this.$properties.keys()) {
            const prop = this.$properties.get(k)
            if (prop?.save) {
                this.__saves = this.__saves || [];
                this.__saves.push(k);
            }
            if (prop?.local) {
                this.__locals = this.__locals || [];
                this.__locals.push(k);
            }
            if (prop?.global) {
                this.__globals = this.__globals || [];
                this.__globals.push(k);
            }
            if (prop?.$notify || prop?.notify) {
                this.__notifications = this.__notifications || new Map();
                let name = typeof prop.notify !== 'string' ? `${k}-changed` : prop.$notify || prop.notify;
                let is$ = !!prop.$notify;
                this.__notifications.set(k, { name, is$ });
            }
            if (prop?.default !== undefined) this[k] = prop.default;
        }

        const name = this.localName.replace('fx-', '');
        this.$url = `${urlFX.replace('fx.js', '')}fx/${name}/${name}.js`;
        this.$ulid = this.$ulid || FX.ulid();
        this.ulid = FX.ulid();
        if (this._useInfo) this.$urlInfo = `${urlFX.replace('fx.js', '')}fx/${name}/$info/$info.js`;
    }
    connectedCallback() {
        super.connectedCallback();
        this._initBus();
        if (this.$$) {
            this.$$.update.listen(this.fnUpdate);
        }
        this._updateSaves(true);
        if (this.$$ && this.__locals) {
            this.$$.listen(this.fnLocals);
            this.__locals.forEach(i => {
                if (this.$$[i] === undefined) this.$$[i] = this[i];
                else this[i] = this.$$[i];
            });

        }
        if (this.$$ && this.__globals) {
            FX.$$.listen(this.fnGlobals);
            this.__globals.forEach(i => {
                if (FX.$$[i] === undefined) FX.$$[i] = this[i];
                else this[i] = FX.$$[i];
            });

        }
        this._partid = this.$partid || this._partid || this.partid;
        if (this.shadowRoot && FX.adoptedStyleSheet) {
            this.shadowRoot.adoptedStyleSheets = [...this.shadowRoot.adoptedStyleSheets, FX.adoptedStyleSheet];
            // console.log('..... adoptedStyleSheets', this.localName);
        }
    }
    disconnectedCallback() {
        if (this._$listeners) for (const i of this._$listeners) i.unlisten();
        if (this.$$?.update) this.$$.update.unlisten(this.fnUpdate);
        if (this.$$ && this.__locals) this.$$.unlisten(this.fnLocals);
        if (FX.$$ && this.__globals) FX.$$.unlisten(this.fnGlobals);
        super.disconnectedCallback();
    }
    _updateSaves(isFirstUpdateSaves) {
        if (this.$$ && this.__saves) {
            const res = {};
            const obj = JSON.parse(localStorage.getItem(this._saveFileName));
            this.__saves.forEach(i => {
                const v = obj?.[this.localName + '.' + i];
                if (v !== undefined) {
                    if (v !== null && v.constructor.name === "Object") {
                        this.$$[i] = this[i] = { ...this[i], ...v };
                    } else {
                        this.$$[i] = this[i] = v;
                    }
                } else {
                    this.$$[i] = this[i];
                }
                res[i] = this[i];
                if (isFirstUpdateSaves)
                    this.$$[i]?.listen && this.$$[i].listen(() => this.fnSave(i));
            });
            this.__enableSave = true;
            // console.log(res);
            return res;
        }
    }
    _initBus() {
        if (this.id?.startsWith('$') || this.$partid || this.$properties.get('$partid') || (!this.$$ && (!this.$root || this.$properties.get('_partid') || this.__saves || this.__locals || this.__globals))) {
            this._partid = this.$partid || this._partid || this.id || this.localName;
            this.$partid = this.$properties.get('$partid') ? this._partid : this.$partid;
            if (this.$$?.update) this.$$.update.unlisten(this.fnUpdate);
            FX._$$[this._partid] ||= { _$: this, _$$: icaro({ update: icaro({ value: 0 }) }) };
            if (this.$properties.get('$partid'))
                FX._$$[this._partid]._$ = this;
        }
    }

    fnUpdate = (e) => { this.requestUpdate() }
    fnLocals = (e) => { if (this.__locals) this.__locals.forEach(i => { if (e.has(i)) this[i] = e.get(i) }) }
    fnGlobals = (e) => { if (this.__globals) this.__globals.forEach(i => { if (e.has(i)) this[i] = e.get(i) }) }
    fnSave = (prop) => {
        let obj = JSON.parse(localStorage.getItem(this._saveFileName));
        obj = obj || {};
        obj[this.localName + '.' + prop] = this[prop];
        localStorage.setItem(this._saveFileName, JSON.stringify(obj));
    }

    get partid() { return this._PARTID || this.$partid || this.$root?.partid || this._partid || undefined }
    get $$() { return FX._$$?.[this.partid]?.['_$$'] ? FX._$$[this.partid]['_$$'] : undefined }
    get $0() { return FX._$$?.[this.partid]?.['_$'] ? FX._$$[this.partid]['_$'] : undefined }
    get $root() {
        try {
            return this.getRootNode()?.host;
        } catch (err) { }
    }
    get _saveFileName() { return ((this.id || this.partid || this.localName.replace('fx-', '')) + '.saves') }
    // $(v) { return this.$$[v].value }
    $id(id) {
        if (!id) return Array.from(this.renderRoot.querySelectorAll('[id]'));
        return this.renderRoot.getElementById(id);
    }
    $refs(ref) {
        const refs = Array.from(this.renderRoot.querySelectorAll('[ref]'));
        if (!ref) return refs;
        let node = undefined;
        if (refs?.length) refs.forEach(i => {
            let _ref = i.getAttribute('ref');
            if (_ref === ref) node = node || i;
        })
        return node;
    }
    get $slotted() {
        let result = [...this.renderRoot?.querySelectorAll('slot')];
        if (result.length) {
            let res = [];
            result.map(i => res = [...res, ...i.assignedElements({ flatten: false })]);
            result = res;
        }
        return result;
    }
    $qs(path) {
        let result = this.renderRoot?.querySelector(path);
        if (!result) {
            for (let i of this.$slotted) {
                result = i.querySelector(path);
                if (result)
                    break;
                else
                    result = null;
            }
        }
        return result;
    }
    $qsa(path) {
        let result = Array.from(this.renderRoot?.querySelectorAll(path));
        for (let i of this.$slotted) {
            const res = i.querySelectorAll(path);
            for (let el of res)
                result.add(el);
        }
        return result;
    }

    firstUpdated() {
        super.firstUpdated();
        if (this.args) Object.keys(this.args).forEach(k => this[k] = this.args[k]);
        this.__isFirstUpdated = true;
    }

    update(changedProps) {
        super.update(changedProps);
        if (!changedProps) return;
        if (changedProps.has('_partid')) {
            this._initBus();
            this.$$.update.listen(this.fnUpdate);
        }
        if (this.args && changedProps.has('args')) Object.keys(this.args).forEach(k => this[k] = this.args[k]);
        for (const prop of changedProps.keys()) {
            if (this.__enableSave && this.__saves && this.__saves.includes(prop)) {
                this.fnSave(prop);
                // console.log(this._saveFileName)
            }
            if (this.__isFirstUpdated) {
                if (this.$$ && this.__locals && this.__locals.includes(prop)) this.$$[prop] = this[prop];
                if (this.$$ && this.__globals && this.__globals.includes(prop)) FX.$$[prop] = this[prop];
            }
            if (this.__notifications && this.__notifications.has(prop)) {
                const event = this.__notifications.get(prop);
                if (event.is$)
                    this.$fire(event.name, { value: this[prop] });
                else
                    this.fire(event.name, { value: this[prop] });
                if (this[event.name]) this[event.name](this[prop], prop);
            }
        }
    }

    $update(property, value) { FX.$update.call(this, property, value) }
    $listen(event, fn) {
        if (!event) return;
        top._$$ ||= {};
        top._$$[event] ||= icaro({ count: 0 });
        top._$$[event].listen(fn || this.fnListen);
        this._$listeners ||= new Set();
        this._$listeners.add(top._$$[event]);
    }
    $unlisten(event, fn) {
        if (!event || !top._$$[event]) return;
        top._$$[event].unlisten(fn || this.fnListen);
        top._$$[event] = undefined;
    }
    $fire(event, value) {
        if (!event) return;
        // top._$$ ||= {};
        // top._$$[event] ||= icaro({ count: 0 });
        if (top._$$?.[event]) {
            top._$$[event].value = value;
            ++top._$$[event].count;
        }
    }

    fnListen = (e) => console.log('...fire ', this.localName, e?.type, e?.detail);
    listen(event, callback, options) { if (event) event.split(',').forEach(i => this.addEventListener(i.trim(), callback || this.fnListen, options)) }
    unlisten(event, callback, options) { if (event) event.split(',').forEach(i => this.removeEventListener(i.trim(), callback || this.fnListen, options)) }
    fire(event, detail = {}) { if (event) requestAnimationFrame(() => this.dispatchEvent(new CustomEvent(event, { bubbles: true, composed: true, detail }))) }
    resetAllDefault() {
        for (const k of this.$properties.keys()) {
            const prop = this.$properties.get(k)
            if (prop?.default !== undefined) this[k] = prop.default;
        }
    }
    sleep = sleep;
    delay = delay;
    async = delay;
}

const sleep = (delay = 0) => new Promise(resolve => setTimeout(resolve, delay));
const delay = (handler, delay = 0) => new Promise((resolve, reject) => {
    const timer = setTimeout(async () => {
        let res = 'ok';
        try {
            if (handler)
                res = handler();
            resolve(res);
        } catch (error) {
            reject(error);
        } finally {
            clearTimeout(timer);
        }
    }, delay)
})

const __$$ = { _$$: {}, $$: {} };
__$$.$$ = icaro({});
__$$.$$.update = icaro({ value: 0 });
const _$temp = {};
_$temp.throttles = new Map();
_$temp.debounces = new Map();
class CFX {
    constructor() {
        this.ulid = ulid;
        this.ulidr = ulidr;
        this.ulidm = monotonicFactory();
        this.icaro = icaro;
        this.$url = urlFX;
    }
    get _$$() { return __$$._$$; }
    get $$() { return __$$.$$; }
    $update(property, value) {
        if (!this.$$) {
            this.requestUpdate();
            return;
        }
        if (!property && this.$$.update) ++this.$$.update.value;
        else if (this.$$[property]) this.$$[property]['value'] = value;
    }

    async createComponent(comp, props = {}) {
        comp = comp || {};
        try {
            if (typeof comp === 'string') {
                comp = comp.replace(`fx-`, '');
                let url = `${urlFX.replace('fx.js', '')}fx` + (comp.includes('.') ? comp : `/${comp}/${comp}.js`);
                await import(url);
                if (comp.includes('.js')) {
                    comp = comp.replace('.js', '');
                    comp = comp.split('/');
                    comp = comp[comp.length - 1];
                }
                const cmp = document.createElement(`fx-${comp}`);
                for (let p in props) cmp[p] = props[p];
                return cmp;
            }
            for (let p in props) comp[p] = props[p];
        } catch (error) { }
        return comp;
    }

    listen(target, event, callback, options) { if (target && event && callback) event.split(',').forEach(i => target.addEventListener(i.trim(), callback, options)) }
    unlisten(target, event, callback, options) { if (target && event && callback) event.split(',').forEach(i => target.removeEventListener(i.trim(), callback, options)) }
    fire(target, event, detail = {}) { if (target && event) target.dispatchEvent(new CustomEvent(event, { detail })) }

    throttle(key, func, delay = 0, immediate = false) {
        let pending = _$temp.throttles.get(key);
        if (pending) return;
        if (immediate) func();
        pending = setTimeout(() => {
            _$temp.throttles.delete(key);
            if (!immediate) func();
        }, delay);
        _$temp.throttles.set(key, pending);
    }
    debounce(key, func, delay = 0, immediate = false) {
        let pending = _$temp.debounces.get(key);
        if (pending) clearTimeout(pending);
        if (!pending && immediate) func();
        pending = setTimeout(() => {
            _$temp.debounces.delete(key);
            func();
        }, delay);
        _$temp.debounces.set(key, pending)
    }

    dates(d = new Date(), isShort) {
        if (typeof d === 'string')
            d = new Date(d);
        const utc = d.toISOString();
        const local = new Date(d.getTime() - (d.getTimezoneOffset()) * 60 * 1000).toISOString().slice(0, -5).replace('T', ' ');
        const ulid = local.replace(' ', '_') + '_' + FX.ulid();
        const short = local.split(' ')[0];
        const monthStr = short.slice(0, -3);
        const year = short.slice(0, -6);
        const date = short.split('-')[2];
        const reverse = short.split('-').reverse().join('-');
        if (isShort) return { utc, local };
        return { utc, local, short, monthStr, date, ulid, year, reverse };
    }
    ulidToDateTime(ulid) {
        return new Date(decodeTime(ulid));
    }
    ulidt(ulid) {
        return new Date(decodeTime(ulid));
    }
    ulidtr(ulid) {
        return new Date(decodeTimer(ulid));
    }
    ulidToUTC(ulid) {
        return this.dates(this.ulidToDateTime(ulid)).utc;
    }
    ulidToLocal(ulid) {
        return this.dates(this.ulidToDateTime(ulid)).local;
    }
    isPlainObject = (v) => !!v && typeof v === 'object' && (v.__proto__ === null || v.__proto__ === Object.prototype);
    isString = (v) => Object.prototype.toString.call(v) === '[object String]';
    typeOf = (v) => {
        return v === null ? 'null' :
            v === undefined ? 'undefined' :
                Object.prototype.toString.call(v).slice(8, -1).toLowerCase();
    }
    sortBy = (arr = [], k = '', _k = '') => {
        return arr.concat().sort((a, b) => {
            const _a = _k ? a[k]?.[_k] : a[k];
            const _b = _k ? b[k]?.[_k] : b[k];
            return _a > _b ? 1 : _a < _b ? -1 : 0;
        })
    }
    MS_DAY = 1000 * 60 * 60 * 24; // ms in day
}

globalThis.FX = new CFX();


class FXRouter {
    constructor() {
        window.addEventListener('popstate', e => this.callback?.(e.state?.path || location.hash));
    }
    create(callback) {
        this.callback = callback;
    }
    go(path) {
        window.history.pushState({ path }, null, path);
        this.callback(path);
    }
}
FX.router = new FXRouter();

FX.sleep = sleep;
FX.async = FX.delay = delay;

// FX.isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);
FX.isMobile = 'ontouchstart' in document.documentElement;

FX.icaroListen = (obj, fn) => {
    if (FX.typeOf(obj) === 'object' || FX.typeOf(obj) === 'array') {
        if (!obj.listen) obj = FX.icaro(obj);
        obj.listen(e => {
            fn && fn(e);
            e.forEach((v, k) => FX.icaroListen(e.get(k), fn));
        }, true)
        if (FX.typeOf(obj) === 'object') {
            Object.keys(obj).forEach(k => obj[k] = FX.icaroListen(obj[k], fn));
        } else if (FX.typeOf(obj) === 'array') {
            for (let i = 0; i < obj.length; i++)
                obj[i] = FX.icaroListen(obj[i], fn);
        }
    }
    return obj;
}

await import('/fx/~/isp/isp.js');

if ('adoptedStyleSheets' in Document.prototype && !FX.adoptedStyleSheet) {
    FX.adoptedStyleSheet = new CSSStyleSheet();
    fetch(FX.$url.replace('fx.js', 'fx/~/styles/styles.css'))
        .then(response => {
            return response.text();
        })
        .then(data => {
            FX.adoptedStyleSheet.replaceSync(data);
        })
}

if (!window.FxRect) {
    window.FxRect = (element) => {
        if (element && element.host) element = element.host;
        const pos = element ? element.getBoundingClientRect() : FX.mousePos;
        return pos ? {
            ok: true, x: pos.x, y: pos.y,
            top: pos.top, bottom: pos.bottom, left: pos.left, right: pos.right,
            width: pos.width, height: pos.height
        } : { ok: false };
    }
}

top._windows ||= [];
const fnWins = (win = window) => {
    try {
        win.addEventListener('pointerdown', (e) => {
            FX.mousePos = new DOMRect(e.pageX, e.pageY);
            top.dispatchEvent(new CustomEvent("_pointerdown", { detail: { target: e.target, pos: FX.mousePos, path: e.path, e } }));
            // console.log('..... _pointerdown - ', e.target, FX.mousePos);
        })
        top._windows.add(win);
        const wins = Array.from(win);
        top._windows.add(...wins);
        wins.forEach(w => fnWins(w));
    } catch (error) {

    }
}
fnWins();
// console.log('..... _windows - ', top._windows);

FX.validString = (str = '', prefix = '', to = '-', def = '', _a) => {
    let a = { "Ё": "YO", "Й": "I", "Ц": "TS", "У": "U", "К": "K", "Е": "E", "Н": "N", "Г": "G", "Ш": "SH", "Щ": "SCH", "З": "Z", "Х": "H", "Ъ": "'", "ё": "yo", "й": "i", "ц": "ts", "у": "u", "к": "k", "е": "e", "н": "n", "г": "g", "ш": "sh", "щ": "sch", "з": "z", "х": "h", "ъ": "'", "Ф": "F", "Ы": "I", "В": "V", "А": "A", "П": "P", "Р": "R", "О": "O", "Л": "L", "Д": "D", "Ж": "ZH", "Э": "E", "ф": "f", "ы": "i", "в": "v", "а": "a", "п": "p", "р": "r", "о": "o", "л": "l", "д": "d", "ж": "zh", "э": "e", "Я": "Ya", "Ч": "CH", "С": "S", "М": "M", "И": "I", "Т": "T", "Ь": "'", "Б": "B", "Ю": "YU", "я": "ya", "ч": "ch", "с": "s", "м": "m", "и": "i", "т": "t", "ь": "'", "б": "b", "ю": "yu" };
    a = { ...a, ...(_a || {}) };
    str = str.split('').map((char) => a[char] || char).join("");
    if (!_a)
        str.replace(/\W/gi, ' ');
    str = str.trim().replace(/ +/gi, to).toLowerCase();
    return prefix + (str || def || FX.ulid());
}
FX.generatePassword = (length = 8, maxSymbCount = 1, symbset = '+-$', charset = '') => {
    maxSymbCount = maxSymbCount >= 1 ? maxSymbCount : 0;
    const crypto = window.crypto || window.msCrypto;
    if (typeof crypto === 'undefined') {
        throw new Error('Crypto API is not supported. Please upgrade your web browser');
    }
    charset ||= '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
    let indexes = crypto.getRandomValues(new Uint32Array(length));
    let secret = '';
    for (const index of indexes) {
        secret += charset[index % charset.length];
    }
    if (symbset && maxSymbCount > 0) {
        indexes = crypto.getRandomValues(new Uint32Array(maxSymbCount));
        let indexes2 = crypto.getRandomValues(new Uint32Array(maxSymbCount)), i = 0;
        for (const index of indexes) {
            let idx = index % length, val = symbset[indexes2[i] % symbset.length];
            secret = secret.slice(0, idx) + val + secret.slice(idx + 1);
            i += 1;
        }
    }
    return secret;
}

FX.switchTheme = (theme, onlySet = false, doc = document) => {
    if (!theme) {
        const currentTheme = localStorage.getItem('fx-theme') || 'light';
        if (!onlySet) {
            theme = currentTheme === 'light' ? 'dark' : 'light';
        } else {
            theme = currentTheme;
        }
    }
    FX._theme = theme;
    localStorage.setItem('fx-theme', theme);
    try {
        let meta = doc.querySelector('meta[name=color-scheme]');
        if (!meta) {
            meta = doc.createElement('meta');
            meta.name = meta.id = 'color-scheme';
            doc.head.appendChild(meta);
        }
        meta.content = theme;
        doc.documentElement.classList.remove('is-light', 'is-dark');
        doc.documentElement.classList.add(`is-${theme}`);
    } catch (error) {
        console.warn('Error setting theme to document:', error);
    }
    top.dispatchEvent(new CustomEvent('fx-theme-changed', { detail: { theme } }));
    return theme;
}
FX._theme = localStorage.getItem('fx-theme');
FX.switchTheme(FX._theme, true);

FX.copyToClipboard = (copyText) => {
    if (navigator.clipboard) {
        navigator.clipboard.writeText(copyText);
    } else {
        const textArea = document.createElement("textarea");
        textArea.value = copyText;
        document.body.appendChild(textArea);
        textArea.focus({ preventScroll: true });
        textArea.select();
        try {
            document.execCommand('copy');
        } catch (err) {
            console.error('Unable to copy to clipboard', err);
        }
        document.body.removeChild(textArea);
    }
}
FX.openFullscreen = (elem) => {
    if (elem.requestFullscreen) {
        elem.requestFullscreen();
    } else if (elem.webkitRequestFullscreen) { /* Safari */
        elem.webkitRequestFullscreen();
    } else if (elem.msRequestFullscreen) { /* IE11 */
        elem.msRequestFullscreen();
    }
}
FX.closeFullscreen = () => {
    if (document.exitFullscreen) {
        document.exitFullscreen();
    } else if (document.webkitExitFullscreen) { /* Safari */
        document.webkitExitFullscreen();
    } else if (document.msExitFullscreen) { /* IE11 */
        document.msExitFullscreen();
    }
}
FX.setIcons = (icons) => {
    if (icons) {
        FX.cachedIcons ||= {};
        Object.keys(icons).map(i => FX.cachedIcons[i] = icons[i]);
    }
}

FX.show = async (host, comp, compProps = {}, hostProps = {}) => {
    host = await FX.createComponent(host, hostProps);
    comp = await FX.createComponent(comp, compProps);
    if (hostProps.data && hostProps.data.host) hostProps.data.host.push(host);
    if (host.show) return host.show(comp);
    host.appendChild(comp);
    document.body.appendChild(host);
    return new Promise((resolve, reject) => {
        host.listen('close', (e) => {
            document.body.removeChild(host);
            resolve({ detail: e.detail?.obj ? e.detail : e.detail.id, value: e.detail, comp });
        })
    })
}
FX.showModal = async (props = { hideTop: true, modal: '280, 120', ok: 'Ok' }, host = top.document.body) => {
    let form = await FX.createComponent('form', props);
    host.appendChild(form);
    return new Promise((resolve, reject) => {
        form.listen('close-form', (e) => {
            host.removeChild(form);
            form = undefined;
            resolve({ detail: e.detail?.obj ? e.detail : e.detail.id });
        })
    })
}
FX.IO = async (io = {}, props = {}, ddProps) => {
    const res = await FX.show('dropdown', 'property-grid', { io, label: 'property grid', ...props }, { ...{ parent: document.body, align: 'modal', showHeader: true, label: io.label || io.name || io._id || 'fx-object-inspector', minWidth: 800 }, ...ddProps });
    return res;
}
FX.colorPicker = async (srcValue, id = 'color-picker', label = 'select color ...') => {
    const res = await FX.show('dropdown', 'oklch', { srcValue, id }, { showHeader: true, label, draggable: true, resizable: false, btnCloseOnly: false });
    return res;
}
FX.showPG = async (pgProps = {}, frmProps = {}, id) => {
    let pg = await FX.createComponent('property-grid', pgProps);
    if (!pg.io)
        pg.io = pgProps;
    pg.slot = 'main';
    let form = await FX.createComponent('form', frmProps);
    form.appendChild(pg);
    form.id = id || pgProps?.io?.id || 'fx-form-pg';
    form.style.zIndex = 999;
    form.listen('close-form', (e) => {
        if (top.document.getElementById(form.id))
            top.document.body.removeChild(form);
    })
    top.document.body.appendChild(form);
}
FX.closePG = (id = 'fx-form-pg') => {
    const form = top.document.getElementById(id);
    if (form)
        top.document.body.removeChild(form);
}
FX.showGrid = async (grProps = {}, ddProps = {}, closeAll = false) => {
    ddProps.id ||= 'fx-show-grid';
    if (closeAll) {
        const dds = [...document.body.getElementsByTagName('fx-dropdown')].filter(i => i.id !== ddProps.id);
        dds.forEach(dd => dd._close());
    }
    if (FX.closeForm(ddProps.id)) return;
    await FX.show('dropdown', 'grid', grProps, { ...{ showHeader: true, label: 'Settings', minWidth: 300 }, ...ddProps });
}
FX.showFGrid = async (props = {}, frmProps = {}, closeAll = false) => {
    frmProps.id ||= 'fx-show-grid';
    if (closeAll) {
        const dds = [...document.body.getElementsByTagName('fx-dropdown')];
        dds.forEach(dd => dd._close());
    }
    if (FX.closeForm(frmProps.id)) return;
    await FX.show('form', 'grid', props, { ...{ showHeader: true, label: 'Settings', minWidth: 300 }, ...frmProps });
}
FX.showForm = async (comp = '', props = {}, frmProps = {}, closeAll = false) => {
    frmProps.id ||= 'fx-show-form';
    if (closeAll) {
        const frms = [...document.body.getElementsByTagName('fx-form')];
        frms.forEach(dd => dd._close());
    }
    if (FX.closeForm(frmProps.id)) return;
    await FX.show('form', comp, props, frmProps);
}
FX.closeForm = (id = 'fx-show-form') => {
    const form = top.document.getElementById(id);
    if (form?._close) {
        form._close();
        return true;
    }
}
FX.closeDD = (id = 'fx-show-form') => {
    const dds = [...document.body.getElementsByTagName('fx-dropdown')];
    dds.forEach(dd => dd._close());
}
FX.showMenu = async (props = {}) => {
    const menu = await FX.createComponent('menu', props);

    if (props.parentElement) {
        menu.parentElement = props.parentElement;
        document.body.appendChild(menu);
        return menu.show();
    } else {
        document.body.appendChild(menu);
        return menu;
    }
}
FX.showJSON = async (json = {}, label) => {
    const str = `
<meta charset="utf-8">
<title>${label || json.label || json.name || json._id || 'fx-json-viewer'}</title>
<script type="module" src="${FX.$url}"></script>
<script type="module" src="${FX.$url.replace('fx.js', 'fx/jmonaco/jmonaco.js')}"></script>
<style>
    html, body { background-color: #1E1E1E; }
    #editor {  opacity: 0; transition: opacity .5s linear; }
    #editor[ready] { opacity: 1; }
</style>
<fx-jmonaco id="editor" mode="json"></fx-jmonaco>
<script type="module">
    editor.setAttribute('ready', true);
    setTimeout(() => {
        editor.lzs="${FX.zcuri(JSON.stringify(json, null, 4))}";
    }, 500)
</script>
    `
    const blob = new Blob([str], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    window.open(url, '_blank');
}
FX.flatItems = (item = {}, addItem = false) => {
    const flat = {};
    if (addItem)
        flat[item._id || item.id || 'root'] = item;
    const fn = (item) => (item?.items || []).map(i => {
        i.parentId = item._id || item.id;
        flat[i._id || i.id] = i;
        fn(i);
    })
    fn(item);
    return flat;
}
FX.allItems = (item = {}, idOnly = false) => {
    const all = [idOnly ? item._id || item.id || 'root' : item];
    const fn = (item, idOnly) => (item?.items || []).map(i => {
        all.push(idOnly ? i._id || i.id : i);
        fn(i, idOnly);
    })
    fn(item, idOnly);
    return all;
}
FX.toDelete = (item = {}, idOnly = false) => {
    const all = [];
    if (item._deleted)
        all.push(idOnly ? item._id || item.id : item);
    const fn = (item, idOnly) => (item?.items || []).map(i => {
        if (i._deleted)
            all.push(idOnly ? i._id || i.id : i);
        fn(i, idOnly);
    })
    fn(item, idOnly);
    return all;
}

FX.mediaPath = 'http://192.168.192.2:8001';
FX.checkUrl = (url) => {
    if (url?.startsWith('/~') || url?.startsWith('~')) {
        if (url?.startsWith('~')) url = url.replace('~', '/');
        if (FX.mediaPath) {
            url = FX.mediaPath + url;
        } else {
            let port = location.port,
                origin = location.origin;
            if (port) {
                origin = origin.replace(port, (+port + 1) + '');
                url = origin + url;
            }
        }
    }
    return url;
}
