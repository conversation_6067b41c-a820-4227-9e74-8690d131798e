import { FxElement, html, css } from '/fx.js';

import { $styles } from './form.x.js';
import '../icon/icon.js';

customElements.define('fx-form', class FxForm extends FxElement {
    static get properties() {
        return {
            hidden: { type: <PERSON>olean },
            fixed: { type: Boolean },
            showFixed: { type: Boolean },
            locked: { type: Boolean },
            txtLabel: { type: String, default: '' },
            txtValue: { type: String, default: '' },
            txtInfo: { type: String, default: '' },
            popLeft: { type: Number, default: 0, save: true },
            popTop: { type: Number, default: 0, save: true },
            popWidth: { type: Number, default: 360, save: true },
            popMinWidth: { type: Number, default: 120 },
            popHeight: { type: Number, default: 240, save: true },
            popMinHeight: { type: Number, default: 64 },
            panelBack: { type: String },
            panelColor: { type: String },
            back: { type: String },
            color: { type: String },
            label: { type: String },
            bottom: { type: String },
            zIdx: { type: Number },
            hideTop: { type: Boolean },
            hideBottom: { type: Boolean },
            modal: { type: String },
            cancel: { type: String },
            ok: { type: String },
            close: { type: String },
            widthOk: { type: Number },
            widthClose: { type: Number },
            widthCancel: { type: Number },
            info0: { type: String, default: '' },
            info1: { type: String, default: '' },
            info2: { type: String, default: '' },
            info3: { type: String, default: '' },
            clsInfo0: { type: String, default: 'fl' },
            clsInfo1: { type: String, default: 'fm' },
            clsInfo2: { type: String, default: 'fsr' },
            clsInfo3: { type: String, default: 'fxs' },
            stlInfo0: { type: String },
            stlInfo1: { type: String },
            stlInfo2: { type: String },
            stlInfo3: { type: String },
            img: { type: Object },
            offsetWidth: { type: Number, default: 0 },
            offsetHeight: { type: Number, default: 0 },
            props: { type: Object },
            opacity: { type: Number, default: .7 },
            boxShadow: { type: String, default: '0px 0px 2px 0px var(--fx-border-color)' },
            action: { type: String, default: '' },
            isReady: { type: Boolean },
            active: { type: Boolean }
        }
    }
    get active() { return FX.$$_activeForm === this }

    async firstUpdated() {
        super.firstUpdated();
        await new Promise((r) => setTimeout(r, 0));
        if (this.modal) {
            const m = this.modal.split(','),
                w = m[0],
                h = m[1],
                ow = this.offsetWidth || this.$root?.offsetWidth || document.body.offsetWidth,
                oh = this.offsetHeight || this.$root?.offsetHeight || document.body.offsetHeight;
            this.popLeft = ow / 2 - (w / 2);
            this.popTop = oh / 2 - (h / 2);
            this.popWidth = w;
            this.popHeight = h;
        }
        if (!this.fixed && !this.hideTop) {
            this.__move = this._move.bind(this);
            this.__up = this._up.bind(this);
            if (this.$root && this.popLeft > this.$root.offsetWidth)
                this.popLeft = this.$root.offsetWidth - this.popWidth;
            this.popLeft = this.popLeft < 0 ? 0 : this.popLeft;
            if (this.$root && this.popTop > this.$root.offsetHeight)
                this.popTop = this.$root.offsetHeight - this.popHeight;
            this.popTop = this.popTop < 0 ? 0 : this.popTop;
        }
        this.setActive();
        this.isReady = true;
    }

    setActive() {
        FX.$$_activeForm = this; 
        this.$update();
    }
    clickBtn(e) {
        const id = e.target.id || 'close';
        if (id==='fixed') {
            this.fixed = !this.fixed;
            return;
        }
        let target = this.$slotted[0];
        let value = this.$slotted[0]?.source || this.$slotted[0]?.src || this.$slotted[0]?.value || '';
        this.props ||= {};
        this.props.show = false;
        this.props.res = id;
        this.props.resolve?.(id);
        this.fire('close-form', { id, value: value || this.$qs('textarea')?.value || '', txt: !!this.txtLabel, target });
        this.fire('close', { id, value: value || this.$qs('textarea')?.value || '', txt: !!this.txtLabel, target });
        // console.log({ id, value: this.$qs('textarea')?.value || '', txt: !!this.txtLabel });
        FX.closeForm(this.id);
        this.$update();
    }

    static styles = [$styles]

    get _styles() {
        return html`
            <style>
                :host {
                    position: ${this.fixed ? 'fixed' : 'absolute'};
                    left: ${this.fixed ? 0 : (this.popLeft || 0) + 'px'};
                    top: ${this.fixed ? 0 : (this.popTop || 0) + 'px'};
                    width: ${this.fixed ? '100vw' : (this.popWidth || 240) + 'px'};
                    height: ${this.fixed ? '100vh' : (this.popHeight || 120) + 'px'};
                    min-width: ${(this.popMinWidth || 120) + 'px'};
                    min-height: ${(this.popMinHeight || 60) + 'px'};
                    color: ${this.color || 'var(--fx-color)'};
                    z-index: ${this.active ? 2000000 : this.zIdx || 1000000};
                    box-shadow: ${this.boxShadow || ''};
                }
                .top-panel, .bottom-panel {
                    color: ${this.panelColor || 'var(--fx-panel-color)'};
                    background: ${this.panelBack || 'var(--fx-panel-background)'};
                }
                .bottom-panel {
                    z-index: ${this.zIdx - 1 || 999999};
                }
            </style>
        `
    }
    get _modalPanel() { 
        if (!this.modal) return null;
        return html`
            <div id="shadow" 
                style="position: fixed; top: 0; bottom: 0; left: 0; right: 0; background: gray;
                opacity: ${this.opacity >= 0 ? this.opacity : .7}; transition: 5s;">
            </div>
        `
    }
    get _topPanel() {
        if (this.hideTop) return null;
        return html`
            <div class="top-panel horizontal w100 center h32" style="min-height: 32px">
                <slot name="top"></slot>
                <label class="flex ellipsis pl4 w100">${this.label}</label>
                <fx-icon id="fixed" class="btn" url="cb:maximize:24" scale=.65 
                    @click="${this.clickBtn}" 
                    style="z-index: 1000003;" 
                    ?hidden=${this.modal || !this.showFixed}>
                </fx-icon>
                <fx-icon class="btnr" url="cb:close:24" @click="${this.clickBtn}" style="z-index: 1000003;"></fx-icon>
            </div>
        `
    }
    get _mainPanel() {
        return html`
            <div class="vertical flex main p4 tr relative">
                <div class="vertical tr">
                    ${this.img ? html`
                        <div class="horizontal ${this.img.align || 'left'} relative w100">            
                            <img class="m4 center w100 h100" 
                                src=${this.img?.src || './fit.png'} 
                                style="opacity: ${this.img?.src ? 1 : .1}; 
                                    min-width: ${this.img?.width || 120}px; 
                                    max-width: ${this.img?.width || 120}px;
                                    min-height: ${this.img?.height || 180}px; 
                                    height: ${this.img?.height || 180}px;"
                            >
                        </div>
                    ` : html``}
                    <label class="p4 ${this.clsInfo || ''} ${this.info ? '' : 'hidden'}" 
                        style=${this.stlInfo || ''}
                    >${this.info}</label>
                    <label class="pr8 p4 ${this.clsInfo0 || ''} vertical center ${this.info0 ? '' : 'hidden'}" 
                        style=${this.stlInfo0 || ''}
                    >${this.info0}</label>
                    <label class="p4 ${this.clsInfo1 || ''} ${this.info1 ? '' : 'hidden'}" 
                        style=${this.stlInfo1 || ''}
                    >${this.info1}</label>
                    <label class="p4 ${this.clsInfo2 || ''} ${this.info2 ? '' : 'hidden'}" 
                        style="word-wrap: break-word; ${this.stlInfo2 || ''}"
                    >${this.info2}</label>
                    <label class="p4 ${this.clsInfo3 || ''} ${this.info3 ? '' : 'hidden'}" 
                        style=${this.stlInfo3 || ''}
                    >${this.info3}</label>
                </div>
                ${this.txtLabel ? html`
                    <div class="vertical w100 h100 relative flex tr">
                        <div class="w100 no-flex wrap p4">${this.txtLabel}</div>
                        <textarea class="horizontal flex h100 center p4 br" .value=${this.txtValue || ''} 
                            style="outline: none; resize: none; width: calc(100% - 10px);"
                        ></textarea>
                    </div>
                ` : html``}
                <slot name="main"></slot>

            </div>
        `
    }
    get _bottomPanel() {
        if (this.hideBottom) return null;
        return html`
            <div class="bottom-panel horizontal relative w100 center" style="bottom: 0">
                <label class="ellipsis pl4 w100">${this.bottom}</label>
                <slot name="bottom" class="flex"></slot>
                <div class="flex"></div>
                ${this.cancel ? html`
                    <div id="cancel" style="min-width: ${this.widthCancel || '80'}px; text-align: center;" class="btnh m4 p2" @click=${this.clickBtn}>${this.cancel}</div>
                ` : html``}
                ${this.ok ? html`
                    <div id="ok" style="min-width: ${this.widthOk || '48'}px; text-align: center;" class="btnh m4 p2" @click=${this.clickBtn}>${this.ok}</div>
                ` : html``}
                ${this.close ? html`
                    <div id="close" style="min-width: ${this.widthClose || '80'}px; text-align: center;" class="btnh m4 p2" @click=${this.clickBtn}>${this.close}</div>
                ` : html``}
            </div>
        `
    }
    get _movePanel() {
        if (this.modal || this.fixed || this.locked) return null;
        return html`
            <div @pointerdown="${e => e.stopPropagation()}">
                <div id="move" @pointerdown="${e => this._down(e, 'move')}"></div>
                <div id="tl" class="resize" @pointerdown="${e => this._down(e, 'resize')}"></div>
                <div id="t"  class="resize" @pointerdown="${e => this._down(e, 'resize')}"></div>
                <div id="tr" class="resize" @pointerdown="${e => this._down(e, 'resize')}"></div>
                <div id="l"  class="resize" @pointerdown="${e => this._down(e, 'resize')}"></div>
                <div id="r"  class="resize" @pointerdown="${e => this._down(e, 'resize')}"></div>
                <div id="br" class="resize" @pointerdown="${e => this._down(e, 'resize')}"></div>
                <div id="b"  class="resize" @pointerdown="${e => this._down(e, 'resize')}"></div>
                <div id="bl" class="resize" @pointerdown="${e => this._down(e, 'resize')}"></div>
            </div>
        `
    }
    render() {
        if (this.hidden || !this.isReady || this.props?.show === false) {
            this.style.opacity = 0;
            return null;
        } else {
            this.style.opacity = 1;
        }
        return html`
            ${this._styles}
            ${this._modalPanel}
            <div class="vertical flex w100 h100 relative overflow-h" 
                    style="color: ${this.color || 'var(--fx-color)'};background: ${this.back || 'var(--fx-background)'};"
                    @click=${this.setActive}>
                ${this._topPanel}
                <div class="vertical relative overflow-y flex">
                    ${this._mainPanel}
                </div>
                ${this._bottomPanel}
            </div>
            ${this._movePanel}
        `
    }

    _down(e, action) {
        if (this.fixed || this.modal || this.locked) return;
        this.setActive();
        if (this.$root)
            this.$root.style.userSelect = 'none';
        this.action = action;
        this._actionId = e.target.id;
        this._lastX = e.pageX;
        this._lastY = e.pageY;
        document.documentElement.addEventListener("pointermove", this.__move, false);
        document.documentElement.addEventListener("pointerup", this.__up, false);
        document.documentElement.addEventListener("pointercancel", this.__up, false);
    }
    _move(e) {
        const movX = e.pageX - this._lastX,
            movY = e.pageY - this._lastY;
        this._lastX = e.pageX;
        this._lastY = e.pageY;
        if (this.action === 'move') {
            this.popLeft += movX;
            this.popTop += movY;
            this.$update();
        }
        if (this.action === 'resize') {
            let x = movX, y = movY, w = this.popWidth, h = this.popHeight, l = this.popLeft, t = this.popTop;
            const move = {
                tl: () => { w = w - x; h = h - y; l += x; t += y; },
                t: () => { h = h - y; t += y; },
                tr: () => { w = w + x; h = h - y; t += y; },
                l: () => { w = w - x; l += x; },
                r: () => { w = w + x; },
                bl: () => { w = w - x; h = h + y; l += x; },
                b: () => { h = h + y; },
                br: () => { w = w + x; h = h + y; },
            }
            move[this._actionId]();
            this.popWidth = w; this.popHeight = h; this.popLeft = l; this.popTop = t;
            this.$update();
        }
    }
    _up() {
        if (this.$root)
            this.$root.style.removeProperty('user-select');
        document.documentElement.removeEventListener("pointermove", this.__move, false);
        document.documentElement.removeEventListener("pointerup", this.__up, false);
        document.documentElement.removeEventListener("pointercancel", this.__up, false);
        if (!this.action) return;
        this.action = '';
        this.popLeft = Math.round(this.popLeft);
        this.popTop = Math.round(this.popTop);
        this.popWidth = Math.round(this.popWidth);
        this.popHeight = Math.round(this.popHeight);
        this.$update();
    }
})
