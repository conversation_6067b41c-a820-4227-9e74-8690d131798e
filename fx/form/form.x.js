import { css } from '/fx.js';

export const $styles = css`
    .main::-webkit-scrollbar { width: 0px; height: 0px; }
    :host {
        border: 1px solid var(--fx-border-color);
        border-radius: 4px;
        overflow: hidden;
    }
    .panel {
        touch-action: none;
    }
    #move {
        position: absolute;
        top: 0px;
        left: 0px;
        height: 35px;
        width: 100%;
        z-index: 101;
        /* border: 1px solid red; */
    }
    #tl { height: 6px; width: 6px; top: 0; left: 0; cursor: nwse-resize }
    #t { height: 6px; top: 0; left: 6px; right: 6px; cursor: ns-resize }
    #tr { height: 6px; width: 6px; top: 0; right: 0; cursor: nesw-resize }
    #l { width: 6px; top: 6px; left: 0; bottom: 6px; cursor: ew-resize }
    #r { width: 6px; top: 6px; right: 0; bottom: 6px; cursor: ew-resize }
    #br { height: 6px; width: 6px; bottom: 0; right: 0; cursor: nwse-resize }
    #b { height: 6px; bottom: 0; left: 6px; right: 6px; cursor: ns-resize }
    #bl { height: 6px; width: 6px; bottom: 0; left: 0; cursor: nesw-resize }
    .resize {
        position: absolute;
        z-index: 110;
        touch-action: none;
        /* border: 1px solid red; */
    }
`

// FX.showCachedIcons = true;
// FX.showLoadIcons = true;
// const usedIcons =
// {
// }
// FX.setIcons(usedIcons);
