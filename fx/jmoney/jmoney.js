import { FxElement, html, css } from '../../fx.js';

import { BS_ITEM } from '../base/src/base-item.js';
import '../icon/icon.js';
import '../table/table.js';

customElements.define('fx-jmoney', class FxJMoney extends FxElement {
    static get properties() {
        return {
            mode: { type: String, default: 'cost', save: true, list: ['cost', 'arrival', 'debts'] },
            action: { type: Object, global: true },
            rows: { type: Array },
            cell: { type: Object, notify: true },
            isTotal: { type: Boolean },
            base: { type: Object },
            showMenu: { type: Boolean, default: false }
        }
    }
    get label() {
        if (this.mode === 'arrival')
            return 'Приход';
        else if (this.mode === 'debts')
            return 'Платежи';
        else
            return 'Движение';
    }
    get table() { return this.$qs('fx-table') }

    firstUpdated() {
        super.firstUpdated();
        setTimeout(() => {
            this.rows = this.cell?.source || { cost: [], arrival: [], debts: [] };
            this.rows.cost ||= [];
            this.rows.arrival ||= [];
            this.rows.debts ||= [];
            this.getTableData();
        }, 100)
    }

    'cell-changed'(e) {
        this.rows = undefined;
        this.getTableData();
    }
    _setMonth(e) {
        // console.log(e.target.value, this.cell);
        if (this.cell) {
            this.cell.month = e.target.value;
        }
    }

    async btnClick(e) {
        this.style.setProperty('--user-select', 'none');
        let act = e.target.id;
        if (act === 'add') {
            this.docsToSave ||= {};
            const _id = this.mode + ':' + FX.ulid();
            const row = { _id, date: FX.dates(new Date()).date };
            this.rows[this.mode].push(row);
            // this.table.data.rows.push(row);
            this.docsToSave[row._id] = row;
            // this.action = { id: 'table-money', fn: '_setRows', scrollToEnd: true };
            if (this.cell)
                this.cell.source = this.rows;
            this.getTableData(1);
            this.async(() => {
                this.table.scrollToRow(this.table.data.length - 1);
                this.table.selectedRowIndex = this.table.data.length - 1;
            }, 100)
        } else if (act === 'delete') {
            if (!this.table.selected) return;
            this.docsToSave ||= {};
            if (this.table.selected._id)
                this.docsToSave[this.table.selected._id] = this.table.selected;
            // this.action = { id: 'table-money', fn: '_deleteRow' };
            this.table.selected._deleted = true;
            if (this.cell)
                this.cell.source = this.rows;
            this.getTableData(1);
            this.async(() => {
                if (this.table.selectedRowIndex > this.table.data.length - 1) {
                    this.table.selectedRowIndex = this.table.data.length - 1;
                    this.table.scrollToRow(this.table.data.length - 1);
                    this.table.selectedRowIndex = this.table.data.length - 1;
                }
            }, 100)
        } else if (act === 'paste') {
            if (this.isTotal) {
                this.rows.arrival = FX._isSourceMoney.arrival;
                this.rows.debts = FX._isSourceMoney.debts;
            } else {
                this.rows = FX._isSourceMoney;
            }
            FX._isSourceMoney = undefined;
            this.getTableData();
            if (this.cell)
                this.cell.source = this.rows;
        } else if (act === 'erase') {
            if (this.rows[this.mode].length) {
                const res = await FX.showModal({ cancel: 'Отмена', ok: 'Ok', modal: '320, 140', info1: 'Очищаем текущую таблицу ?', info2: this.label });
                if (res?.detail === 'ok') {
                    this.rows[this.mode] = [];
                    this.getTableData();
                    if (this.cell)
                        this.cell.source = this.rows;
                }
            }
        } else if (act === 'copy') {
            FX._isSourceMoney = { ...{}, ...this.rows };
            if (this.isTotal) {
                FX._isSourceMoney.cost = [];
            }
        }
        this.$update();
        FX.debounce('user-select', () => { this.style.setProperty('--user-select', 'auto') }, 500)
    }
    setMode(mode) {
        this.mode = mode;
        this.getTableData();
    }
    loadNewData(e) {
        const data = e.detail?.data;
        if (!data) return;
        this.rows[this.mode] = data;
        this.cell.source = this.rows;
        this.getTableData(1);
    }

    static get styles() {
        return css`
            :host {
                flex: 1;
                height: 100%;
                overflow: hidden;
                user-select: var(--user-select);
             }
             input {
                background: transparent!important;
             }
            .menu-dropdown {
                position: absolute;
                top: 30px;
                right: 80px;
                background: light-dark(white, var(--fx-panel-background));
                border: 1px solid var(--fx-border-color);
                border-radius: 6px;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
                z-index: 1000;
                min-width: 100px;
                padding: 6px;
                display: flex;
                flex-direction: column;
                gap: 2px;
                animation: menuFadeIn 0.15s ease-out;
            }
            @keyframes menuFadeIn {
                from {
                    opacity: 0;
                    transform: translateY(-4px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
        `
    }

    render() {
        return html`
            <div class="horizontal w100">
                <div class="horizontal allign pt4 h32">
                    <fx-icon url="fe-edit" class="ml6" scale="0.8" size="28" an="btn" style="cursor: pointer" @click=${() => this.setMode('cost')} fill=${this.mode === 'cost' ? 'blue' : 'gray'}></fx-icon>
                    <fx-icon url="fe-folder-plus" class="ml8" scale="0.8" size="28" an="btn" style="cursor: pointer" @click=${() => this.setMode('arrival')} fill=${this.mode === 'arrival' ? 'blue' : 'gray'}></fx-icon>
                    <fx-icon url="fe-link" class="ml8" scale="0.8" size="28" an="btn" style="cursor: pointer" @click=${() => this.setMode('debts')} fill=${this.mode === 'debts' ? 'blue' : 'gray'}></fx-icon>
                </div>

                <input type="month" class="fm inp flex horizontal center h32" .value=${this.cell?.month} .hidden=${this.isTotal} @change=${this._setMonth}>
                <div class="fm flex horizontal center" .hidden=${!this.isTotal}>${this.base?.fxSelected?.label}</div>
                <div class="horizontal align ${this.isTotal && this.mode === 'cost' ? 'hidden' : ''}">
                    <fx-icon class="ml2 mr8" url="carbon:menu" scale=.9 an="btn" @click=${e => this.showMenu = !this.showMenu}></fx-icon>
                    ${this.showMenu ? html`
                        <div class="menu-dropdown absolute" @click=${(e) => e.stopPropagation()}>
                            ${FX._isSourceMoney ? html`
                                <fx-icon id="paste" url="cb-table-built" fill="red" class="mr8" scale="0.8" size="28" an="btn" style="cursor: pointer" @click=${this.btnClick} txt="paste"></fx-icon>
                            ` : html``}
                            <fx-icon id="copy" url="cb-copy" class="mr8" scale="0.8" size="28" an="btn" style="cursor: pointer" @click=${this.btnClick} txt="copy"></fx-icon>
                            <fx-icon id="erase" url="bs-eraser" class="mr8" scale="0.8" size="28" an="btn" style="cursor: pointer" @click=${this.btnClick} txt="erase"></fx-icon>
                        </div>
                    ` : html``}
                    <fx-icon id="delete" url="fe-minus" fill="red" class="mr8" scale="0.8" size="28" an="btn" style="cursor: pointer" @click=${this.btnClick} title="delete row"></fx-icon>
                    <fx-icon id="add" url="fe-plus" fill="blue" class="mr4" scale="0.8" size="28" an="btn" style="cursor: pointer" @click=${this.btnClick} title="add row"></fx-icon>
                </div>
            </div>
            <fx-table id=${'table-money-' + this.mode} class="brt horizontal flex w100" slot="main" @change-row=${this.changeTableRow} style="height: calc(100% - 32px)" hide=${this.isTotal ? 'c' : 'cn'} @loadNewData=${this.loadNewData} mode=${this.mode}></fx-table>
        `
    }

    async getTableData(opacity = 1) {
        this.rows ||= this.cell?.source || { cost: [], arrival: [], debts: [] };
        const options0 = {
            typeColumn: 'input',
            sortColumns: ['date', 'sort'],
        }
        const columns0 = [
            // {
            //     "header": "№",
            //     "field": "$idx",
            //     "width": 40,
            //     "idx": 0,
            //     "left": 60
            // },
            {
                "header": "дата",
                "field": "date",
                "width": 50,
                "idx": 1,
                "left": 210,
                textAlign: 'center',
                footerStyle: 'justify-content: center;',
                // style: `font-size: 12px;`,

            },
            {
                "header": "sort",
                "field": "sort",
                "width": 40,
                "idx": 1,
                "left": 210,
                textAlign: 'center'
                // style: `font-size: 12px;`,
            },
            {
                "header": "наименование",
                "field": "name",
                "width": 120,
                "idx": 1,
                "left": 210,
                "textAlign": "left"
            },
            {
                "header": "операция",
                "field": "oper",
                "width": 200,
                "idx": 1,
                "left": 210,
                "textAlign": "left"
            },
            {
                "header": "сумма",
                "field": "sum",
                "width": 80,
                "idx": 5,
                "left": 789,
                "textAlign": "right",
                footerStyle: 'justify-content: end;'
            },
            {
                "header": "дата",
                "field": "date2",
                "width": 50,
                "idx": 1,
                "left": 210,
                textAlign: 'center',
            },
            {
                "header": "приход",
                "field": "oplata",
                "width": 80,
                "idx": 5,
                "left": 789,
                "textAlign": "right",
                footerStyle: 'justify-content: end;'
            },
            { field: 'info', header: 'Inf', width: 32, typeColumn: 'info' },
            { header: 'Примечание', field: 'note', showTitle: true, typeColumn: 'textarea', minWidth: 240, width: 'auto', style: 'font-size: 14px; text-wrap: auto;' },
        ]
        const options1 = {
            typeColumn: 'input',
            sortColumns: ['date', 'sort'],
        }
        const columns1 = [
            // {
            //     "header": "№",
            //     "field": "$idx",
            //     "width": 40,
            //     "idx": 0,
            //     "left": 60
            // },
            {
                "header": "sort",
                "field": "sort",
                "width": 40,
                "idx": 1,
                "left": 210,
                textAlign: 'center',
                footerStyle: 'justify-content: center;',
                // style: `font-size: 12px;`,
            },
            {
                "header": "контрагент",
                "field": "name",
                "width": 120,
                "idx": 1,
                "left": 210
            },
            {
                "header": "сумма",
                "field": "summa",
                "width": 90,
                "idx": 2,
                "left": 460,
                "textAlign": "right",
                footerStyle: 'justify-content: end;'
            },
            {
                "header": "остаток",
                "field": "sum",
                "width": 90,
                "idx": 5,
                "left": 789,
                "textAlign": "right",
                footerStyle: 'justify-content: end;'
            },
            {
                "header": "start",
                "field": "start",
                "idx": 3,
                "width": 90,
                "left": 565,
                style: `font-size: 14px;`,
            },
            {
                "header": "end",
                "field": "end",
                "idx": 4,
                "width": 90,
                "left": 669,
                style: `font-size: 14px;`,
            },
            {
                "header": "%",
                "field": "percent",
                "idx": 4,
                "width": 60,
                "left": 669,
                "textAlign": "right",
            },
            {
                "header": "платеж",
                "field": "plat",
                "width": 80,
                "idx": 5,
                "left": 789,
                "textAlign": "right",
                footerStyle: 'justify-content: end;'
            },
            {
                "header": "дата",
                "field": "date",
                "width": 50,
                "idx": 1,
                "left": 210,
                textAlign: 'center',
            },
            {
                "header": "оплачено",
                "field": "oplata",
                "width": 80,
                "idx": 5,
                "left": 789,
                "textAlign": "right",
                footerStyle: 'justify-content: end;'
            },
            { field: 'info', header: 'Inf', width: 32, typeColumn: 'info' },
            { header: 'Примечание', field: 'note', showTitle: true, typeColumn: 'textarea', minWidth: 240, width: 'auto', style: 'font-size: 14px; text-wrap: auto;' },
        ]

        const options2 = {
            typeColumn: 'input',
            sortColumns: ['date', 'sort'],
        }
        const columns2 = [
            // {   
            //     "header": "№",
            //     "field": "$idx",
            //     "width": 40,
            //     "idx": 0,
            //     "left": 60,
            //     autoHide: this.isTotal ? true : false
            // },
            {
                "header": "дата",
                "field": "date",
                "width": this.isTotal ? 81 : 50,
                "left": 210,
                textAlign: 'center',
                style: `font-size: ${this.isTotal ? '12px' : ''};`,
                typeColumn: 'input',
                footerStyle: 'justify-content: center;',
            },
            {
                "header": "sort",
                "field": "sort",
                "width": 40,
                "idx": 1,
                "left": 210,
                textAlign: 'center',
                // style: `font-size: 12px ;`,
                autoHide: this.isTotal ? true : false
            },
            {
                "header": "контрагент / операция",
                "field": "oper",
                "width": 240,
                "idx": 1,
                "left": 210,
                "textAlign": "left",
                typeColumn: 'input'
            },
            {
                "header": "сумма",
                "field": "sum",
                "width": 80,
                "idx": 5,
                "left": 789,
                "textAlign": "right",
                footerStyle: 'justify-content: end;'
            },
            {
                "header": "в планах",
                "field": "plan",
                "width": 80,
                "idx": 5,
                "left": 789,
                "textAlign": "right",
                footerStyle: 'justify-content: end;',
                autoHide: this.isTotal ? true : false
            },
            { 
                header: 'тип', 
                field: 'type', 
                width: 90, 
                typeColumn: 'select', 
                options: ['', '... ', '++', '--разное', '--еда', '--необх', '--план', '--неплан', '--авто', '--развлеч', '--подарок', '--траты', '--здоровье', '--красота', '--услуги', '--гаджеты', '--одежда', '--дом', '--животные', '++план', '++неплан', '++подарок'],
                class: 'fs'
            },
            { field: 'info', header: 'Inf', width: 32, typeColumn: 'info' },
            { header: 'Примечание', field: 'note', showTitle: true, typeColumn: 'textarea', minWidth: 240, width: 'auto', style: 'font-size: 14px; text-wrap: auto;' },
            {
                "header": "долги",
                "field": "dolgi",
                "width": 90,
                "idx": 5,
                "left": 789,
                "textAlign": "right",
                footerStyle: 'justify-content: end;',
                autoHide: this.isTotal ? true : false
            },
            {
                "header": "резерв - накопления",
                "field": "rezerv",
                "width": 160,
                "idx": 5,
                "left": 789,
                "textAlign": "right",
                footerStyle: 'justify-content: end;',
                autoHide: this.isTotal ? true : false,
            },
        ]
        this.table.style.opacity = opacity;
        this.$update();
        if (this.mode === 'arrival') {
            this.options = options0;
            this.columns = columns0;
            this.data = this.rows.arrival.filter(i => !i._deleted);
            this.footerCalculations = {
                date: { type: 'count' },
                sum: { type: 'sum', decimals: 0 },
                oplata: { type: 'sum', decimals: 0 },
            }
            this.topLabel = 'Приход';
        } else if (this.mode === 'debts') {
            this.options = options1;
            this.columns = columns1;
            this.data = this.rows.debts.filter(i => !i._deleted);
            this.footerCalculations = {
                sort: { type: 'count' },
                sum: { type: 'sum', decimals: 0 },
                summa: { type: 'sum', decimals: 0 },
                oplata: { type: 'sum', decimals: 0 },
                plat: { type: 'sum', decimals: 0 },
            }
            this.topLabel = 'Платежи';
        } else if (this.mode === 'cost') {
            this.options = options2;
            this.columns = columns2;
            this.data = this.rows.cost.filter(i => !i._deleted);
            this.footerCalculations = {
                date: { type: 'count' },
                sum: { type: 'sum', decimals: 0 },
                dolgi: { type: 'sum', decimals: 0 },
                plan: { type: 'sum', decimals: 0 },
                rezerv: { type: 'sum', decimals: 0 },
            }
            this.topLabel = 'Движение';
        }
        setTimeout(async () => {
            this.table.topLabel = this.topLabel;
            if (this.isTotal) {
                let data = await this.getTotalItem();
                data = data[this.mode];
                this.data = data?.filter(i => !i._deleted);
            }
            this.table.data = this.data;
            this.table.options = this.options;
            this.table.columns = this.columns;
            this.table.footerCalculations = this.footerCalculations;
            this.$update();
            this.async(() => {
                // this.table.updateVisibleRows();
                this.table.applySortColumns(this.table.options.sortColumns);
                this.table.style.opacity = 1;
                this.table.$update();
            })
        }, 100)
    }
    async getTotalItem(selected = this.base.fxSelected, fx = this.base.fxSelected, type = 'moneyTotal') {
        let _id = type + ':' + selected._id,
            item = BS_ITEM.changesMap.get(_id);
        if (!item) {
            let doc;
            try { doc = await this.base.dbLocal.get(_id) } catch (err) { }
            item = new BS_ITEM({ _id, type, isLoad: true, doc: (doc || { parentId: selected._id }) });
        }
        let children = BS_UTILS.allItems(fx).filter(i => i.is === 'money'),
            keys = children.map(i => 'money:' + i._id),
            allDocs = await this.base.dbLocal.allDocs({ keys, include_docs: true }),
            cost = [];
        (allDocs || []).rows.map(async i => {
            let _cost = i.doc?.source?.cost || [];
            _cost = [...[], ..._cost];
            let __cost = [];
            _cost.filter(c => !c.type?.startsWith('...')).map(c => {
                let _c = { ...c };
                _c.date = i.doc.month + '-' + _c.date;
                __cost.push(_c);
            })
            cost = [...cost, ...__cost]
            item.doc.source ||= {};
            item.doc.source.cost = cost;
        })
        return item.doc?.source || {};
    }
})

// FX.showCachedIcons = true;
// FX.showLoadIcons = true;

const usedIcons =
{
    "fc-currency_exchange": "<svg version=\"1\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 48 48\" enable-background=\"new 0 0 48 48\">\r\n    <circle fill=\"#3F51B5\" cx=\"18\" cy=\"18\" r=\"15\"/>\r\n    <path fill=\"#FFF59D\" d=\"M20.3,16v1.7h-3.8v1.4h3.8v1.7h-3.8c0,0.6,0.1,1.2,0.3,1.6c0.2,0.4,0.4,0.8,0.7,1c0.3,0.3,0.7,0.4,1.1,0.6 c0.4,0.1,0.9,0.2,1.4,0.2c0.4,0,0.7,0,1.1-0.1c0.4-0.1,0.7-0.1,1-0.3l0.4,2.7c-0.4,0.1-0.9,0.2-1.4,0.2c-0.5,0.1-1,0.1-1.5,0.1 c-0.9,0-1.8-0.1-2.6-0.4c-0.8-0.2-1.5-0.6-2-1.1c-0.6-0.5-1-1.1-1.4-1.9c-0.3-0.7-0.5-1.6-0.5-2.6h-1.9v-1.7h1.9v-1.4h-1.9V16h1.9 c0.1-1,0.3-1.8,0.6-2.6c0.4-0.7,0.8-1.4,1.4-1.9c0.6-0.5,1.3-0.9,2.1-1.1c0.8-0.3,1.7-0.4,2.6-0.4c0.4,0,0.9,0,1.3,0.1 c0.4,0.1,0.9,0.1,1.3,0.3l-0.4,2.7c-0.3-0.1-0.6-0.2-1-0.3c-0.4-0.1-0.7-0.1-1.1-0.1c-0.5,0-1,0.1-1.4,0.2c-0.4,0.1-0.8,0.3-1,0.6 c-0.3,0.3-0.5,0.6-0.7,1s-0.3,0.9-0.3,1.5H20.3z\"/>\r\n    <circle fill=\"#4CAF50\" cx=\"30\" cy=\"30\" r=\"15\"/>\r\n    <path fill=\"#fff\" d=\"M28.4,27c0.1,0.2,0.2,0.4,0.4,0.6c0.2,0.2,0.4,0.4,0.7,0.5c0.3,0.2,0.7,0.3,1.1,0.5c0.7,0.3,1.4,0.6,2,0.9 c0.6,0.3,1.1,0.7,1.5,1.1c0.4,0.4,0.8,0.9,1,1.4c0.2,0.5,0.4,1.2,0.4,1.9c0,0.7-0.1,1.3-0.3,1.8c-0.2,0.5-0.5,1-0.9,1.4 s-0.9,0.7-1.4,0.9c-0.6,0.2-1.2,0.4-1.8,0.5v2.2h-1.8v-2.2c-0.6-0.1-1.2-0.2-1.8-0.4s-1.1-0.5-1.5-1c-0.5-0.4-0.8-1-1.1-1.6 c-0.3-0.6-0.4-1.4-0.4-2.3h3.3c0,0.5,0.1,1,0.2,1.3c0.1,0.4,0.3,0.6,0.6,0.9c0.2,0.2,0.5,0.4,0.8,0.5c0.3,0.1,0.6,0.1,0.9,0.1 c0.4,0,0.7,0,0.9-0.1c0.3-0.1,0.5-0.2,0.7-0.4c0.2-0.2,0.3-0.4,0.4-0.6c0.1-0.2,0.1-0.5,0.1-0.8c0-0.3,0-0.6-0.1-0.8 c-0.1-0.2-0.2-0.5-0.4-0.7s-0.4-0.4-0.7-0.5c-0.3-0.2-0.7-0.3-1.1-0.5c-0.7-0.3-1.4-0.6-2-0.9c-0.6-0.3-1.1-0.7-1.5-1.1 c-0.4-0.4-0.8-0.9-1-1.4c-0.2-0.5-0.4-1.2-0.4-1.9c0-0.6,0.1-1.2,0.3-1.7c0.2-0.5,0.5-1,0.9-1.4c0.4-0.4,0.9-0.7,1.4-1 c0.5-0.2,1.2-0.4,1.8-0.5v-2.4h1.8v2.4c0.6,0.1,1.2,0.3,1.8,0.6c0.5,0.3,1,0.6,1.3,1.1c0.4,0.4,0.7,1,0.9,1.6c0.2,0.6,0.3,1.3,0.3,2 h-3.3c0-0.9-0.2-1.6-0.6-2c-0.4-0.4-0.9-0.7-1.5-0.7c-0.3,0-0.6,0.1-0.9,0.2c-0.2,0.1-0.4,0.2-0.6,0.4c-0.2,0.2-0.3,0.4-0.3,0.6 c-0.1,0.2-0.1,0.5-0.1,0.8C28.3,26.5,28.4,26.8,28.4,27z\"/>\r\n</svg>\r\n",

    "fe-folder-plus": "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"feather feather-folder-plus\"><path d=\"M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z\"></path><line x1=\"12\" y1=\"11\" x2=\"12\" y2=\"17\"></line><line x1=\"9\" y1=\"14\" x2=\"15\" y2=\"14\"></line></svg>",
    "fe-link": "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"feather feather-link\"><path d=\"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71\"></path><path d=\"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71\"></path></svg>",
    "fe-plus": "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"feather feather-plus\"><line x1=\"12\" y1=\"5\" x2=\"12\" y2=\"19\"></line><line x1=\"5\" y1=\"12\" x2=\"19\" y2=\"12\"></line></svg>",
    "fe-minus": "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"feather feather-minus\"><line x1=\"5\" y1=\"12\" x2=\"19\" y2=\"12\"></line></svg>",
    "fe-edit": "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"feather feather-edit\"><path d=\"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7\"></path><path d=\"M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z\"></path></svg>",
    "cb-copy": "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\"><path fill=\"currentColor\" d=\"M28 10v18H10V10h18m0-2H10a2 2 0 0 0-2 2v18a2 2 0 0 0 2 2h18a2 2 0 0 0 2-2V10a2 2 0 0 0-2-2Z\"/><path fill=\"currentColor\" d=\"M4 18H2V4a2 2 0 0 1 2-2h14v2H4Z\"/></svg>",
    "bs-eraser": "<svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"currentColor\" class=\"bi bi-eraser\" viewBox=\"0 0 16 16\">\r\n  <path d=\"M8.086 2.207a2 2 0 0 1 2.828 0l3.879 3.879a2 2 0 0 1 0 2.828l-5.5 5.5A2 2 0 0 1 7.879 15H5.12a2 2 0 0 1-1.414-.586l-2.5-2.5a2 2 0 0 1 0-2.828l6.879-6.879zm2.121.707a1 1 0 0 0-1.414 0L4.16 7.547l5.293 5.293 4.633-4.633a1 1 0 0 0 0-1.414l-3.879-3.879zM8.746 13.547 3.453 8.254 1.914 9.793a1 1 0 0 0 0 1.414l2.5 2.5a1 1 0 0 0 .707.293H7.88a1 1 0 0 0 .707-.293l.16-.16z\"/>\r\n</svg>",
    "cb-table-built": "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\"><path fill=\"currentColor\" d=\"M28 8h-4V4a2.002 2.002 0 0 0-2-2H4a2.002 2.002 0 0 0-2 2v18a2.002 2.002 0 0 0 2 2h4v4a2.002 2.002 0 0 0 2 2h18a2.002 2.002 0 0 0 2-2V10a2.002 2.002 0 0 0-2-2Zm-6 14h-8v-5h8Zm0-7h-8v-5h8Zm-10 0H4v-5h8ZM22 4v4H4V4ZM4 22v-5h8v5Zm24 6H10v-4h12a2.002 2.002 0 0 0 2-2V10h4Z\"/></svg>"
}
FX.setIcons(usedIcons);
