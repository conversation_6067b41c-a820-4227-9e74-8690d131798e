import { FxElement, html, css } from '/fx.js';
import { $styles } from './ftmp.x.js';
import '../button/button.js';
import '../checkbox/checkbox.js';
import '../property-grid/property-grid.js';
import '../table/table.js';
import '../tree/tree.js';
import '../splitter/splitter.js';
import { BS_ITEM } from '../base/src/base-item.js';

export class FxFtmp extends FxElement {
    static properties = {
        base: { type: Object },
        item: { type: Object, notify: true },
        selectedField: { type: Object },
        label: { type: String },
        isReady: { type: Boolean, default: false },
        highlightSelected: { type: Boolean, default: false, save: true },
        showConstructor: { type: Boolean, default: false },
    }

    get flat() { return FX.flatItems(this.item?.fields || {}, true) }
    get root() { return this.item?.fields || {} }
    get label() { return this.root?.label0 || '' }

    firstUpdated() {
        super.firstUpdated();
        setTimeout(() => {
            this.init();
            this.isReady = true;
            this.style.opacity = 1;
            this.$update();
        }, 100)
    }
    'item-changed'(e) {
        if (!this.isReady) return;
        this.init();
    }
    async init() {
        const db = this.base?.dbLocal;
        if (!db || !this.base.fxSelected?.if?.ifForm) return;
        if (this.base.bsFlatDrafts['form-item-' + this.base.fxSelected._id]) {
            this.item = this.base.bsFlatDrafts['form-item-' + this.base.fxSelected._id];
            this.selectedField = this.item?.fields;
            this.$update();
            return;
        }
        let _id, useParentConstructor, allDocs2;
        if (!this.base.fxSelected.if.ifConstructor) {
            let parent = this.base.fxFlat[this.base.fxSelected.parentId];
            while (parent) {
                if (parent?.if.ifConstructor) {
                    _id = parent._id;
                    useParentConstructor = true;
                    break;
                }
                parent = this.base.fxFlat[parent._id];
            }
        } else {
            _id ||= this.base.fxSelected._id;
        }
        if (!_id) {

            return;
        }
        let key = 'field:' + _id;
        const allDocs = await db.allDocs({ include_docs: true, startkey: key, endkey: key + '\ufff0' });
        const flat = {};
        let root;
        if (useParentConstructor) {
            key = 'field:' + this.base.fxSelected._id;
            allDocs2 = await db.allDocs({ include_docs: true, startkey: key, endkey: key + '\ufff0' });
        }
        allDocs?.rows.map(i => {
            const doc = i.doc;
            let _id = doc._id;
            delete doc._rev;
            if (useParentConstructor) {
                _id = doc._id = key + ':' + _id.split(':').at(-1);
                if (doc.parentId) {
                    doc.parentId = key + ':' + doc.parentId.split(':').at(-1);
                }
            }
            const item = new BS_ITEM({ _id, type: 'field', isLoad: true, doc });
            if (_id.endsWith(':$root-field'))
                root = item;
            else
                flat[_id] = item;
        })
        allDocs2?.rows.map(i => {
            let doc = i.doc;
            const _id = doc._id;
            if (!_id.endsWith(':$root-field') && flat[_id]?.doc)
                doc = { ...flat[_id].doc, ...doc }
            const item = new BS_ITEM({ _id, type: 'field', isLoad: true, doc });
            flat[_id] = item;
        })
        this.item.fields = await this.base.buildTree(flat, key + ':$root-field', 'field', root);
        this.item.fields.icon ||= 'flat-color-icons:services:32';
        this.item.fields.label ||= 'fields';
        this.selectedField = this.item?.fields;
        this.base.bsFlatDrafts ||= {};
        this.base.bsFlatDrafts['form-item-' + this.base.fxSelected._id] = this.item;
        this.$update();
    }
    on_fieldSelected(e) {
        const item = e.detail?.item || this.fxFlat?.[e.detail?.row?._id];
        this.selectedField = item;
        this.async(() => {
            if (item._id.endsWith('$root-field') && this.isShowFieldsSets) {
                FX.closeDD();
                this.isShowFieldsSets = false;
                this.showFormSets();
            } else if (this.isShowFieldsSets) {
                this.$update();
                const grid = document.querySelectorAll('fx-grid')[0];
                grid?.$update();
            } else if (!item._id.endsWith('$root-field') && this.isShowFormSets) {
                FX.closeDD();
                this.isShowFormSets = false;
                this.showFieldSets();
            }
        }, 20)
        this.$update();
    }
    async on_fieldAdd(e) {
        if (!this.selectedField?._id) return;
        const key = 'field:' + this.base.fxSelected._id,
            _id = key + ':' + FX.ulid();
        const newItem = new BS_ITEM({ _id, label: 'new field', isNew: true, type: 'field', parentId: this.selectedField._id });
        this.selectedField.items ||= [];
        this.selectedField.items.push(newItem);
        this.selectedField.expanded = true;
        newItem.sort = this.selectedField.items.length;
        this.$update();
    }
    showSets() {
        if (this.selectedField._id?.endsWith('$root-field')) {
            this.showFormSets();
        } else {
            this.showFieldSets(this.selectedField);
        }
    }

    static styles = $styles.ftmp

    render() {
        return html`
            <div class="form horizontal flex w100 h100 relative" @click=${this.formClick}>
                ${this.showConstructor ? html`  
                    <fx-tree id="fx-ftmp-tree" .item=${this.item?.fields} style="min-width: 164px;  width: 280px;" @selected=${this.on_fieldSelected} @add-item=${this.on_fieldAdd} remoteAddItem>
                        <div class="horizontal flex" slot="panel">
                            <div class="flex"></div>
                            <fx-icon id="btn-tree-sets" class="but ml8" url="flat-color-icons:services" scale=.8 an="btn" br="square" @click=${this.showSets}></fx-icon>
                        </div> 
                    </fx-tree>
                    <fx-splitter color="gray" vertical size="1"></fx-splitter>
                ` : html``}
                <div class="form w100 h100 relative ${this.root.class || ''}" style=${this.root.style} @click=${this.formClick}>
                    <div class="w100 brb horizontal mh36 p8 center no-flex box ${this.label ? '' : 'hidden'} ${this.root.class0 || ''}" style=${this.root.style0 || ''}>${this.label || ''}</div>
                    <div class="vertical flex h100 w100 relative overflow-h">
                        <div class="w100 vertical overflow-y flex relative">
                            <fx-ftmp-field .main=${this} .field=${this.root}></fx-ftmp-field>
                        </div>
                    </div>
                </div>
            </div>
        `
    }

    async showFormSets() {
        const run = async (e, item) => {
            let _id = item._id;
            this.$update();
            this.$qs('fx-tree').$update();
            document.querySelectorAll('fx-grid')[0].$update();
        }
        const item = this.dataFormSets = () => [
            { id: 'highlightSelected', fill: 'var(--fx-color-selected)', icon: 'ant-design:alert-outlined:28', label: 'highlight selected', subLabel: 'подсвечивать выбранные', get: () => this.highlightSelected, is: 'checkbox', set: (e) => this.highlightSelected = e, run },
            {
                id: 'forms-set', icon: 'flat-color-icons:services:28', label: 'form sets', subLabel: 'установки формы', value: this.root._id, is: 'span', expanded: true, items: [
                    { id: 'class', icon: '', label: 'form class', subLabel: 'класс формы', get: () => this.root.class || '', set: (e) => this.root.class = e, run, is: 'textarea' },
                    { id: 'style', icon: '', label: 'form style', subLabel: 'стиль формы', get: () => this.root.style || '', set: (e) => this.root.style = e, run, is: 'textarea' },
                ]
            },
            {
                id: 'label', icon: '', label: 'form label', subLabel: 'метка формы', get: () => this.root.label0 || '', set: (e) => this.root.label0 = e, run, expanded: true, items: [
                    { id: 'label-class', icon: '', label: 'label class', subLabel: 'класс метки', get: () => this.root.class0 || '', set: (e) => this.root.class0 = e, run, is: 'textarea' },
                    { id: 'label-style', icon: '', label: 'label style', subLabel: 'стиль метки', get: () => this.root.style0 || '', set: (e) => this.root.style0 = e, run, is: 'textarea' },
                ]
            },
        ]
        this.isShowFormSets = true;
        await FX.showGrid({ type: 'form-sets', id: this._id + '-form-sets', item: item(), rowHeight: 32, hideSelected: true }, { id: this._id + '-form-sets', label: 'Form settings', intersect: true, align: 'left', class: 'br', minWidth: 300, draggable: true, resizable: false, btnCloseOnly: true }, true);
        this.isShowFormSets = false;
    }
    async showFieldSets() {
        const run = async (e, item) => {
            let id = item._id || item.id;
            if (id === 'selectIcon') {
                let res = await FX.show('dropdown', '/icons/icons.js', { isSelectIcon: true }, {});
                if (res?.detail?.res) {
                    let spl = res.detail.res.split(':'),
                        icon = spl[0] + (spl[1] ? ':' + spl[1] : '');
                    this.selectedField.fIcon = icon;
                }
            }
            if (id === 'is') {
                const v = item.value;
                const sets = {
                    'row': 'lucide:rectangle-horizontal',
                    'table': 'carbon:table-split',
                    'tab': 'fluent-mdl2:folder-horizontal',
                    'tabs': 'carbon:collapse-all',
                    'label': 'carbon:label',
                    'icon': 'carbon:image',
                    'dummy': 'bi:border',
                    'input': 'vaadin:input',
                    'c-label': 'ph:text-indent'
                }
                this.selectedField.icon = sets[v] || '';
                if (v && (this.selectedField.label === 'new field' || this.selectedField.label === '...'))
                    this.selectedField.label = v;
            }
            this.$update();
            this.$qs('fx-tree').$update();
            document.querySelectorAll('fx-grid')[0].$update();
        }

        const item = this.dataFieldSets = () => [
            {
                id: 'id', icon: 'flat-color-icons:tree-structure:28', label: 'field id', subLabel: 'id поля', get: () => this.selectedField._id || '', set: (e) => this.selectedField._id = e.value, run, is: 'span', expanded: true, items: [
                    { id: 'name', icon: '', label: 'name', subLabel: 'имя поля', get: () => this.selectedField.label || '', set: (e) => this.selectedField.label = e, run },
                    { id: 'is', icon: '', label: 'field is', subLabel: 'is поля', get: () => this.selectedField.is || '', set: (e) => this.selectedField.is = e, is: 'select', options: ['', 'row', 'label', 'c-label', 'tabs', 'tab', 'table', 'icon', 'input', 'dummy'], run },
                    { id: 'isType', icon: '', label: 'input type', subLabel: 'тип input поля', get: () => this.selectedField.isType || '', set: (e) => this.selectedField.isType = e, is: 'select', options: ['', 'text', 'checkbox', 'button', 'color', 'date', 'datetime-local', 'email', 'hidden', 'image', 'month', 'number', 'password', 'radio', 'range', 'reset', 'search', 'submit', 'tel', 'time', 'url', 'week'], run },
                    { id: 'class', icon: '', label: 'class', subLabel: 'класс поля', get: () => this.selectedField.class || '', set: (e) => this.selectedField.class = e, is: 'textarea', run },
                    { id: 'style', icon: '', label: 'style', subLabel: 'стиль поля', get: () => this.selectedField.style || '', set: (e) => this.selectedField.style = e, is: 'textarea', run },
                    { id: 'style', icon: '', label: 'container style', subLabel: 'стиль контейнера', get: () => this.selectedField.data?.fstyle || '', set: (e) => { this.selectedField.data ||= {}; this.selectedField.data.fstyle = e }, is: 'textarea', run },
                ]
            },
            {
                id: 'iconName', icon: 'flat-color-icons:gallery:28', label: 'icon name', subLabel: 'иконка', get: () => this.selectedField.fIcon, set: (e) => this.selectedField.fIcon = e, run, items: [
                    { id: 'selectIcon', icon: 'flat-color-icons:gallery:28', label: 'set icon', value: 'select icon', is: 'button', run },
                    { id: 'sizeIcon', icon: 'flat-color-icons:edit-image:28', label: 'icon size', subLabel: 'размер иконки', get: () => this.selectedField.fIconSize || 24, set: (e) => this.selectedField.fIconSize = e, type: 'number', run },
                    { id: 'deleteIcon', icon: 'fx:close:28', fill: 'red', label: 'delete icon', value: 'delete icon', is: 'button', run },

                ]
            },
        ]
        this.isShowFieldsSets = true;
        await FX.showGrid({ type: 'fields-sets', id: this._id + '-fields-sets', item: item(), rowHeight: 32, hideSelected: true }, { id: this._id + '-fields-sets', label: 'Fields settings', intersect: true, align: 'left', class: 'br', minWidth: 300, draggable: true, resizable: false, btnCloseOnly: true }, true);
        this.isShowFieldsSets = false;
    }
}
customElements.define('fx-ftmp', FxFtmp);

customElements.define('fx-ftmp-field', class FxFtmpField extends FxElement {
    static properties = {
        main: { type: Object },
        item: { type: Object },
        root: { type: Object },
        selectedField: { type: Object },
        field: { type: Object },
        flex: { type: String, default: 'unset' }
    }
    get item() { return this.main?.item || {} }
    get selectedField() { return this.main?.selectedField || {} }
    get root() { return this.main?.item?.fields || {} }

    firstUpdated() {
        super.firstUpdated();
        setTimeout(() => {
            this.style.setProperty('--flex', this.flex || 'unset');
            this.isReady = true;
            this.$update();
        }, 100)
    }
    isSelected(i) {
        if (this.main.highlightSelected)
            return this.selectedField?._id === i._id ? 'selected2' : ''
        return false;
    }
    tableData(doc) {
        // let d;
        // try {
        //     d = JSON.parse(i.tableData || '{}')
        // } catch (error) {
        //     console.log(error);
        // }
        // d ||= {};
        // return d;
    }

    _fieldClick(doc) {
        // let act = doc.iconAction,
        //     item = this.item,
        //     table,
        //     docTable,
        //     data;
        // if (act) {
        //     table = this.$qs('#' + doc.iconTableID);
        //     docTable = Object.values(item.$flatFields || {}).find(i => i.doc.tableID === doc.iconTableID)?.doc;
        //     data = this._tableData[docTable.ulid];
        //     // console.log(table);
        //     if (!table || !docTable)
        //         return;
        // } else
        //     return;
        // const acts = {
        //     'addRowTable': () => {
        //         data.rows ||= [];
        //         data.rows.push({});
        //         this.action = { id: docTable.tableID, fn: '_setRows', scrollToEnd: true };
        //         docTable.tableData = JSON.stringify(data);
        //     },
        //     'deleteRowTable': () => {
        //         // docTable.tableData = '';
        //         this.action = { id: docTable.tableID, fn: '_deleteRow' };
        //         data.rows = table._rows;
        //         this.async(() => {
        //             docTable.tableData = JSON.stringify(data);
        //         }, 100)
        //     },
        //     'clearTable': () => {
        //         data.rows = [];
        //         docTable.tableData = JSON.stringify(data);
        //     },
        // }
        // acts[act] && acts[act]();
        // this.$update();
    }
    setTableData() {
        this.data = {};
        this.data.topLabel = 'top-label';
        this.data.bottomLabel = 'bottom-label';
        this.data.columns = [
            { field: 'id', header: 'ID', width: 64 },
            { field: 'selector1', header: 'S', width: 40, typeColumn: 'selector1', args: { size: 28, color1: 'violet' } },
            { field: 'selector', header: 'Selector', width: 130, typeColumn: 'selector', args: { size: 28, type: '●' }, style: 'margin-top: -6px;' },
            { field: 'date', header: 'Date', width: 150, typeColumn: 'input', typeInput: 'date' },
            { field: 'ta', header: 'TA', width: 150, typeColumn: 'textarea', style: 'color: gray;' },
            { field: 'name', header: 'Name', width: 150, typeColumn: 'input' },
            { field: 'email', header: 'Email', width: 200 },
            { field: 'phone', header: 'Phone', width: 150, typeColumn: 'input', typeInput: 'tel' },
            { field: 'price', header: 'Price', width: 100 },
            { field: 'actions', header: 'Actions', width: 80, typeColumn: 'html' },
            { field: 'info', header: 'Inf', width: 32, typeColumn: 'info' },
            { field: 'note', header: 'Note', minWidth: 200, width: 'auto' },
        ]
        this.data.data = Array.from({ length: 1000 }, (_, i) => ({
            id: i + 1,
            name: `User ${i + 1}`,
            email: `user${i + 1}@example.com`,
            phone: `******-${Math.floor(1000 + Math.random() * 9000)}`,
            price: Math.floor(100 + Math.random() * 900),
            actions: '<fx-button width="auto">Edit</button>',
            selector: '0, 0, 0, 0, 0',
            selector1: '',
            date: '',
            ta: 'textarea ... textarea ... textarea ... textarea ...',
            note: 'note ... note ... note ... note ...'
        }))
        this.data.footerCalculations = {
            id: { type: 'count' },
            price: { type: 'sum', decimals: 2 },
            name: { type: 'custom', customFn: (data) => `Total: ${data.length} users` }
        }
    }

    static styles = css`
        :host {
            display: flex;
            flex-direction: column;
            justify-content: start;
            /* flex: var(--flex, unset); */
            position: relative;
            flex-wrap: wrap;
            box-sizing: border-box;
            margin: 4px;
        }
        .selected2 {
            /* box-shadow: 0 4px 2px -2px var(--fx-color-selected); */
            outline: 2px solid var(--fx-color-selected);
        }
    `

    _isRow(i) {
        return html`
            <div class="horizontal w100 wrap ${this.isSelected(i)} ${i.class || ''}" style="${this.root?.style || ''}; ${i?.style || ''}">
                ${i?.items?.map(f => {
            return html`
                <fx-ftmp-field .main=${this.main} class="${f.class || ''} ${this.isSelected(f)}" .field=${{ items: [f] }} flex='1'></fx-ftmp-field>
            `
        })}
            </div>
        `
    }
    _isTabs(i) {
        return html`
            <div class="vertical flex h100 w100 relative ${i?.class || ''}" style="${this.root.style || ''}; ${i.style || ''}">
                <div class="horizontal w100 brb mt4 ${this.isSelected(i)}">
                    ${(i.items || []).filter(d => !d._deleted).map((t, tidx) => {
                        return html`
                            <div class="tabs m2 p4 br pointer ${this.isSelected(t)} ${tidx === (this.tidx || 0) ? 'selected' : ''}" style=" border-bottom: 0; margin-bottom: 0; border-radius: 4px 4px 0 0;" @click=${e => { this.tidx = tidx; this.$update(); }}>
                                ${t.label}
                            </div>
                        `
                    })}
                </div>
                <div class="w100 vertical overflow-y flex relative" style="min-height: 0">
                    <fx-ftmp-field .main=${this.main} class="m4" .field=${i.items?.[this.tidx || 0]}></fx-ftmp-field>
                </div>
            </div>
        `
    }
    _isLabel(i) {
        return html`
            <div class="w100 pl8 ${i.items?.length ? 'min-w100' : ''} ${i.class || ''} ${this.isSelected(i)}" style="${this.root.style || ''} ${i.style || ''}">${i.label}</div>
        `
    }
    _isIcon(i) {
        return html`
            <fx-icon url=${i.iconName || 'fx:menu'} size=${i.iconSize || 24} class="${i.class || ''} ${this.isSelected(i)}" style="${this.root.style || ''} ${i.style || ''}" an="btn" @click=${e => this._fieldClick(i)} fill=${i.iconColor || ''} svg=${i.iconSVG || ''} scale=${i.iconScale || ''} title=${i.iconTitle || ''}></fx-icon>
        `
    }
    _isTable(i) {
        this.setTableData();
        const data = this.data;
        // this._tableData ||= {};
        // this._tableData[doc.ulid] ||= this.tableData(doc);
        // let data = [{}, {}, {}]; // this._tableData?.[doc.ulid];
        return html`
            <div class="horizontal relative w100 overflow-h bral box ${this.isSelected(i)}" style="height: 500px">  
                <fx-table .id=${i.tableID} .data=${data.data} .columns=${data?.columns} class="w100 ${i.class || ''} ${this.isSelected(i)}" style="${this.root.style || ''} ${i.style || ''}">${i.label}></fx-table>
            </div>
        `
    }
    _isInput(i) {
        return html`
            <fx-ftmp-input .main=${this.main} class="${this.root.class || ''} ${i.class || ''} ${this.isSelected(i)}" .field=${i} style="${this.root.style || ''} ${i.style || ''}">
            </fx-ftmp-input>
    `
    }
    _isDummy(i) {
        return html`
            <div class="${i.class || ''} ${this.isSelected(i)}" style="${this.root.style || ''} ${i.style || ''}"></div>
        `
    }

    render() {
        return html`
            ${this.field?.items?.map(i => {
                return i._deleted ? html`` : html`
                    ${i.is === 'input' || i.is === 'c-label' ? this._isInput(i)
                    : i.is === 'row' ? this._isRow(i)
                        : i.is === 'tabs' ? this._isTabs(i)
                            : i.is === 'label' ? this._isLabel(i)
                                : i.is === 'icon' ? this._isIcon(i)
                                    : i.is === 'table' ? this._isTable(i)
                                        : i.is === 'dummy' ? this._isDummy(i)
                                            : this._isInput(i)
                }`
            })}
        `
    }
})

customElements.define('fx-ftmp-input', class FxFtmpInput extends FxElement {
    static properties = {
        main: { type: Object },
        item: { type: Object },
        root: { type: Object },
        field: { type: Object },
        selectedField: { type: Object }
    }
    get item() { return this.main?.item || {} }
    get selectedField() { return this.main?.selectedField || {} }
    get root() { return this.item?.fields?.[0] || [] }
    get hasChildren() { return this.field?.items?.length }

    _toggleExpand(e) {
        this.field.expanded = e.target.toggled;
        this.$update();
    }

    _change(e) {
        this.field.value = e.target.value;
        this.$update();
    }

    static styles = css`
        :host {
            display: flex;
            flex-direction: column;
            /* box-shadow: -1px -1px 0 0 lightgray; */
            position: relative;
            flex: 1;
            /* min-width: 240px; */
        }
        .row {
            min-height: 34px;
        }
        .show {
            /* visibility: visible; */
            display: block;
        }
        .hide {
            /* visibility: hidden; */
            display: none;
        }
        .fieldset {
            border-radius: 4px;
        }
    `

    render() {
        return html`
            <div style="${this.field.data.fstyle}">
                ${this.field?.is === 'c-label' ? html`
                    <div class="box row horizontal align w100 h100 m0 p0 relative" style="max-width: calc(100% - 16px);">
                        <fx-button class="${this.hasChildren ? 'show' : 'hide'}" name="cb-chevron-right" toggledClass="right90" .toggled="${this.field.expanded}" size="18" back="transparent" border=0 @click=${this._toggleExpand}></fx-button>
                        <fx-icon class="${this.hasChildren ? 'mr8 ml0' : 'ml4'} ${this.field?.fIcon ? 'show' : 'hide'}" icon=${this.field?.fIcon} scale=${this.field?.fIconScale || 1} fill=${this.field?.fIconFill || ''} size=${this.field?.fIconSize || 24}></fx-icon>
                        <div class="${this.hasChildren ? 'hide' : 'show'}" style="width: 8px;"></div>
                        <label type=${this.field?.isType || 'text'} class="w100 inp fm ${this.field?.class || ''}" style="height: 20px; ${this.root.style || ''} ${this.field?.style || ''}">
                            ${this.field?.label}
                        </label>
                        <div style="width: 8px;"></div>
                    </div>
                ` : html`
                    <fieldset class="box fieldset bral p0 m6 relative overflow-h ${this.root.fieldsetsClass || ''} ${this.field.fieldsetClass || ''}" style="${this.root.fieldsetsStyle || ''} ${this.field.fieldsetStyle || ''}">
                        <legend style="height: 20px; padding: 0; font-size: smaller;">${this.field?.label}</legend>
                        <div class="row horizontal align w100 h100 m0 p0 relative" style="margin-top: -8px;">
                            <fx-button class="${this.hasChildren ? 'show' : 'hide'}" name="cb-chevron-right" toggledClass="right90" .toggled="${this.field.expanded}" size="18" back="transparent" border=0 @click=${this._toggleExpand}></fx-button>
                            <fx-icon class="${this.hasChildren ? 'mr8 ml0' : 'ml4'} ${this.field?.fIcon ? 'show' : 'hide'}" icon=${this.field?.fIcon} scale=${this.field?.fIconScale || 1} fill=${this.field?.fIconFill || ''} size=${this.field?.fIconSize || 24}></fx-icon>
                            <div class="${this.hasChildren ? 'hide' : 'show'}" style="width: 8px;"></div>
                            <input type=${this.field?.isType || 'text'} class="w100 inp fm ${this.field?.class || ''}" style="height: 20px; ${this.root.style || ''} ${this.field?.style || ''}" .value=${this.field.value || ''} @change=${this._change} @input=${this._change}>
                            <div style="width: 8px;"></div>
                        </div>
                    </fieldset>
                `}
                ${this.hasChildren && this.field.expanded ? html`
                    <fx-ftmp-field class="ml16" .main=${this.main} .field=${this.field}></fx-ftmp-field>
                ` : html``}
            </div>
        `
    }
})
