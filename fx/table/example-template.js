// Table Template Configuration Example
// Пример конфигурации шаблона таблицы

table.data = {
    columns: [
        { field: 'id', header: 'ID', width: 64 },
        { field: 'date', header: 'Date', width: 150, typeColumn: 'input', typeInput: 'date' },
        {
            field: 'name',
            header: 'Название',
            width: 150,
            typeColumn: 'input'
        },
        {
            field: 'kol',
            header: 'Количество',
            width: 100,
            typeColumn: 'input',
            textAlign: 'right',
            footerStyle: 'justify-content: end;'
        },
        {
            field: 'price',
            header: 'Цена',
            width: 100,
            typeColumn: 'input',
            textAlign: 'right',
            footerStyle: 'justify-content: end;'
        },
        {
            field: 'total',
            header: 'Итого',
            width: 120,
            typeColumn: 'span',
            textAlign: 'right',
            footerStyle: 'justify-content: end;',
            calc: (e) => (e.kol || 0) * (e.price || 0)
        },
        {
            field: 'note',
            header: 'Примечание',
            minWidth: 200,
            width: 'auto',
            typeColumn: 'textarea'
        }
    ],
    footerCalculations: {
        id: { type: 'count' },
        kol: { type: 'sum', decimals: 2 },
        total: { type: 'sum', decimals: 2 },
        name: { type: 'custom', customFn: (data) => `Total: ${data.length} names` }
    },
    options: {
        headerClass: 'custom-header',
        footerClass: 'custom-footer',
        // headerStyle: 'background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;',
        // footerStyle: 'background: #f5f5f5; font-weight: bold;',
        sortColumns: [
            {
                field: 'date',
                direction: 'desc'
            }
        ]
    },
    data: [{}]
}

/*
Типы колонок (typeColumn):
- input - поле ввода
- textarea - многострочное поле  
- selector - выпадающий список
- checkbox - чекбокс
- span - только для отображения
- html - HTML контент

Типы вычислений футера (footerCalculations):
- sum - сумма значений
- count - количество записей
- avg - среднее значение
- custom - пользовательская функция

Функции вычисления (calc):
- (e) => e.field1 * e.field2 - умножение полей
- (e) => (e.price || 0) * (1 + (e.tax || 0)/100) - с проверкой на null
- (e) => e.date ? new Date(e.date).getFullYear() : '' - работа с датами

Выравнивание текста (textAlign):
- left - по левому краю (по умолчанию)
- center - по центру
- right - по правому краю

Стили:
- style - CSS стили для ячеек
- headerStyle - CSS стили для заголовка
- footerStyle - CSS стили для футера
- class - CSS классы
*/
