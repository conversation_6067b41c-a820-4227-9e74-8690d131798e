# Smart Template Editors - Умные редакторы шаблонов

## Обзор

Реализована умная логика для редакторов шаблонов таблиц, которая автоматически определяет, что редактировать:
- **Есть настройки** → редактируются текущие настройки таблицы
- **Нет настроек** → загружается базовый шаблон из файла или создается дефолтный

## Два типа редакторов

### 1. JSON редактор (`showTemplateEditor`)
- **Назначение**: Простое редактирование структуры данных
- **Формат**: Чистый JSON
- **Режим**: Monaco Editor в режиме JSON
- **Подходит для**: Быстрого редактирования структуры

### 2. JavaScript редактор (`showTemplateEditorJS`)
- **Назначение**: Расширенное редактирование с примерами
- **Формат**: JavaScript модуль с `export default`
- **Режим**: Monaco Editor в режиме JavaScript
- **Подходит для**: Сложных настроек с комментариями и примерами

## Умная логика определения контента

### Проверка существующих настроек
```javascript
const hasExistingSettings = this.columns?.length > 0 || 
                           Object.keys(this.footerCalculations || {}).length > 0 || 
                           Object.keys(this.options || {}).length > 0;
```

### Если есть настройки (JSON редактор)
```javascript
if (hasExistingSettings) {
    templateData = {
        columns: this.columns || [],
        footerCalculations: Object.entries(this.footerCalculations).map(([field, config]) => ({
            field,
            type: config.type || 'sum'
        })),
        options: this.options || {}
    };
}
```

### Если есть настройки (JavaScript редактор)
```javascript
if (hasExistingSettings) {
    const templateData = {
        columns: this.columns || [],
        footerCalculations: this.footerCalculations || {},
        options: this.options || {}
    };
    
    jsSource = `// Table Template Configuration
// Текущие настройки таблицы

export default ${JSON.stringify(templateData, null, 4)};`;
}
```

### Если нет настроек
```javascript
else {
    // Пытаемся загрузить из файла
    try {
        const response = await fetch('/fx/table/example-template.js');
        jsSource = await response.text();
    } catch (error) {
        // Fallback к базовому шаблону
        const defaultTemplate = { /* базовая структура */ };
        jsSource = `export default ${JSON.stringify(defaultTemplate, null, 4)};`;
    }
}
```

## Преобразование данных

### footerCalculations: объект ↔ массив
```javascript
// Из объекта в массив (для редактирования)
Object.entries(this.footerCalculations).map(([field, config]) => ({
    field,
    type: config.type || 'sum'
}))

// Из массива в объект (после редактирования)
this.footerCalculations = {};
parsedData.footerCalculations.forEach(calc => {
    if (calc.field && calc.type) {
        this.footerCalculations[calc.field] = { type: calc.type };
    }
});
```

## Обработка ошибок

### JSON редактор
- Валидация JSON формата
- Информативные сообщения об ошибках парсинга

### JavaScript редактор
- Удаление комментариев перед парсингом
- Fallback с `eval()` для сложных объектов
- Обработка отсутствия файла example-template.js

## Тестирование

Создан файл `test-smart-editors.html` с четырьмя сценариями:

1. **Очистить таблицу** - тест загрузки базового шаблона
2. **Базовые настройки** - тест редактирования простых настроек
3. **Расширенные настройки** - тест с вычислениями и стилями
4. **Сложные настройки** - тест с множественными типами колонок

## Преимущества

1. **Интуитивность**: Пользователь всегда видит релевантный контент
2. **Гибкость**: Два формата для разных потребностей
3. **Надежность**: Fallback механизмы для всех сценариев
4. **Удобство**: Автоматическое определение контекста

## Использование

```javascript
// Программное использование
table.showTemplateEditor(e);     // JSON редактор
table.showTemplateEditorJS(e);   // JavaScript редактор

// Через UI
// 1. Открыть настройки таблицы
// 2. Раздел "templates"
// 3. Выбрать нужный редактор
```

Умные редакторы значительно улучшают пользовательский опыт, автоматически адаптируясь к контексту использования.
