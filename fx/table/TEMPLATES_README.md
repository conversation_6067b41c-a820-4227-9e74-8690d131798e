# Table Templates - Шаблоны таблиц

Добавлена функциональность для сохранения и загрузки шаблонов таблиц, включающих настройки columns, footerCalculations и options.

## Новые возможности

### 1. Свойство templateId
- Добавлено новое свойство `templateId` в `static properties` класса `FxTable`
- Автоматически сохраняется в localStorage (save: true)
- При установке templateId автоматически загружается соответствующий шаблон

### 2. Методы для работы с шаблонами

#### `saveTemplate(templateName)`
- Сохраняет текущие настройки таблицы как шаблон в базу данных
- Параметры: `templateName` - название шаблона
- Возвращает: `templateId` при успехе, `false` при ошибке
- Сохраняет: columns, footerCalculations, options, метаданные

#### `loadTemplate(templateId)`
- Загружает шаблон из базы данных по ID
- Параметры: `templateId` - ID шаблона для загрузки
- Возвращает: `true` при успехе, `false` при ошибке
- Применяет загруженные настройки к таблице

#### `getTemplatesList()`
- Получает список всех доступных шаблонов
- Возвращает: массив объектов с информацией о шаблонах
- Структура: `{id, name, created, tableId, mode}`

#### `deleteTemplate(templateId)`
- Удаляет шаблон из базы данных
- Параметры: `templateId` - ID шаблона для удаления
- Возвращает: `true` при успехе, `false` при ошибке

### 3. Пользовательский интерфейс

#### Новый раздел "Templates" в showSets
Добавлен новый раздел в меню настроек таблицы с тремя опциями:

1. **Template Editor** - редактор шаблонов
   - Открывает property-grid для редактирования настроек
   - Позволяет изменять columns, footerCalculations, options

2. **Save Template** - сохранение шаблона
   - Открывает диалог для ввода названия шаблона
   - Сохраняет текущие настройки в базу данных

3. **Load Template** - загрузка шаблона
   - Показывает список доступных шаблонов
   - Позволяет выбрать и загрузить шаблон

### 4. Автоматическая загрузка

#### При инициализации
- В `firstUpdated()` добавлена автозагрузка шаблона, если установлен `templateId`

#### При изменении templateId
- Добавлен обработчик `'templateId-changed'` для автоматической загрузки при изменении

## Структура документа шаблона в базе данных

```javascript
{
  _id: 'table-template:' + ulid,
  type: 'table-template',
  name: 'Template Name',
  tableId: 'table-id',
  mode: 'table-mode',
  created: 'UTC timestamp',
  columns: [...], // массив настроек колонок
  footerCalculations: {...}, // объект вычислений футера
  options: {...} // объект опций таблицы
}
```

## Использование

### Программное использование

```javascript
// Установка templateId для автозагрузки
table.templateId = 'table-template:existing-id';

// Сохранение текущих настроек как шаблон
const templateId = await table.saveTemplate('My Template');

// Загрузка шаблона
const success = await table.loadTemplate(templateId);

// Получение списка шаблонов
const templates = await table.getTemplatesList();

// Удаление шаблона
const deleted = await table.deleteTemplate(templateId);
```

### Через пользовательский интерфейс

1. Откройте настройки таблицы (кнопка с шестеренкой)
2. Найдите раздел "templates" (шаблоны таблицы)
3. Используйте доступные опции:
   - "template editor" - для редактирования
   - "save template" - для сохранения
   - "load template" - для загрузки

## Требования

- Необходимо наличие `this.base.dbLocal` (подключение к локальной базе данных)
- Используется PouchDB для хранения шаблонов
- Требуется FX.ulid() для генерации уникальных ID
- Используется FX.dates() для временных меток

## Тестирование

Для тестирования функциональности используйте файл `test-templates.html`, который содержит:
- Инициализацию тестовой базы данных
- Создание таблицы с тестовыми данными
- Настройку columns, footerCalculations и options
- Инструкции по тестированию

## Совместимость

Функциональность полностью совместима с существующим кодом таблицы и не нарушает работу других компонентов.
