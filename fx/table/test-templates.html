<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="color-scheme" content="light dark" />
    <title>Table Templates Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
        }
        .info {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        fx-table {
            width: 100%;
            height: 400px;
            border: 1px solid #ccc;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Table Templates Test</h1>
        
        <div class="info">
            <h3>Инструкция по тестированию:</h3>
            <ol>
                <li>Откройте настройки таблицы (кнопка с шестеренкой)</li>
                <li>Найдите раздел "templates" (шаблоны таблицы)</li>
                <li>Используйте "template editor" для редактирования настроек</li>
                <li>Используйте "save template" для сохранения шаблона</li>
                <li>Используйте "load template" для загрузки сохраненного шаблона</li>
            </ol>
        </div>

        <fx-table id="testTable"></fx-table>
    </div>

    <script type="module">
        import './table.js';
        import '../base/base.js';

        // Инициализация базы данных
        const initDB = async () => {
            if (!window.FX.PouchDB) {
                await import('/fx/~/pouchdb/pouchdb-9.0.0.min.js');
                window.FX.PouchDB = PouchDB;
            }
            
            const dbLocal = new window.FX.PouchDB('test-templates-db');
            
            // Создаем mock base объект
            const mockBase = {
                dbLocal: dbLocal
            };
            
            return mockBase;
        };

        // Инициализация таблицы
        const initTable = async () => {
            const table = document.getElementById('testTable');
            const base = await initDB();
            
            table.base = base;
            table.id = 'test-table';
            table.mode = 'test';
            
            // Тестовые данные
            table.data = Array.from({ length: 50 }, (_, i) => ({
                id: i + 1,
                name: `User ${i + 1}`,
                email: `user${i + 1}@example.com`,
                phone: `******-${Math.floor(1000 + Math.random() * 9000)}`,
                price: Math.floor(100 + Math.random() * 900),
                date: new Date(2024, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1).toISOString().split('T')[0],
                status: ['Active', 'Inactive', 'Pending'][Math.floor(Math.random() * 3)]
            }));

            // Тестовые колонки
            table.columns = [
                { field: 'id', label: 'ID', width: 60, textAlign: 'center' },
                { field: 'name', label: 'Name', width: 150 },
                { field: 'email', label: 'Email', width: 200 },
                { field: 'phone', label: 'Phone', width: 150 },
                { field: 'price', label: 'Price', width: 100, textAlign: 'right' },
                { field: 'date', label: 'Date', width: 120 },
                { field: 'status', label: 'Status', width: 100 }
            ];

            // Тестовые вычисления футера
            table.footerCalculations = {
                id: { type: 'count' },
                price: { type: 'sum' }
            };

            // Тестовые опции
            table.options = {
                headerClass: 'custom-header',
                footerClass: 'custom-footer',
                sortColumns: [{ field: 'name', direction: 'asc' }]
            };

            console.log('Table initialized with test data');
        };

        // Запуск инициализации
        document.addEventListener('DOMContentLoaded', () => {
            initTable().catch(console.error);
        });
    </script>
</body>
</html>
