# Исправление проблемы с calc функциями в редакторах шаблонов

## Проблема

При редактировании шаблонов таблиц вычисляемые поля с `calc` функциями не попадали в редактор, потому что:
- `JSON.stringify()` не может сериализовать функции
- Функции терялись при преобразовании в JSON/JavaScript код
- После редактирования calc поля не работали

## Решение

Реализована специальная обработка calc функций в обоих редакторах:

### 1. JSON редактор (`showTemplateEditor`)

#### При создании шаблона для редактирования:
```javascript
// Специальная обработка колонок для сохранения calc функций
const processedColumns = (this.columns || []).map(col => {
    const processedCol = { ...col };
    // Преобразуем функцию calc в строку
    if (typeof processedCol.calc === 'function') {
        processedCol.calc = processedCol.calc.toString();
    }
    return processedCol;
});
```

#### При применении изменений:
```javascript
// Специальная обработка колонок для восстановления calc функций
this.columns = parsedData.columns.map(col => {
    const processedCol = { ...col };
    // Преобразуем строку calc обратно в функцию
    if (typeof processedCol.calc === 'string' && processedCol.calc.trim()) {
        try {
            // Проверяем, является ли строка функцией
            if (processedCol.calc.includes('=>') || processedCol.calc.startsWith('function')) {
                processedCol.calc = eval(`(${processedCol.calc})`);
            }
        } catch (error) {
            console.warn('Failed to parse calc function:', processedCol.calc, error);
            // Оставляем как строку, если не удалось преобразовать
        }
    }
    return processedCol;
});
```

### 2. JavaScript редактор (`showTemplateEditorJS`)

#### При создании шаблона для редактирования:
```javascript
// Специальная сериализация для сохранения функций
const serializeWithFunctions = (obj) => {
    return JSON.stringify(obj, (key, value) => {
        if (typeof value === 'function') {
            return value.toString();
        }
        return value;
    }, 4);
};

jsSource = `export default ${serializeWithFunctions(templateData)};`;
```

#### При применении изменений:
```javascript
// Специальная обработка колонок для восстановления calc функций
if (parsedData.columns) {
    this.columns = parsedData.columns.map(col => {
        const processedCol = { ...col };
        // Преобразуем строку calc обратно в функцию
        if (typeof processedCol.calc === 'string' && processedCol.calc.trim()) {
            try {
                if (processedCol.calc.includes('=>') || processedCol.calc.startsWith('function')) {
                    processedCol.calc = eval(`(${processedCol.calc})`);
                }
            } catch (error) {
                console.warn('Failed to parse calc function:', processedCol.calc, error);
            }
        }
        return processedCol;
    });
}
```

## Поддерживаемые форматы calc функций

### Arrow функции:
```javascript
calc: (e) => (e.qty || 0) * (e.price || 0)
calc: (e) => Math.round(e.total * 0.2 * 100) / 100
```

### Обычные функции:
```javascript
calc: function(e) { return e.qty * e.price; }
```

### Сложные функции:
```javascript
calc: (e) => {
    const total = (e.qty || 0) * (e.price || 0);
    const tax = Math.round(total * 0.2 * 100) / 100;
    return Math.round((total + tax) * 100) / 100;
}
```

## Примеры в редакторах

### JSON редактор покажет:
```json
{
    "field": "total",
    "header": "Итого",
    "calc": "(e) => (e.qty || 0) * (e.price || 0)"
}
```

### JavaScript редактор покажет:
```javascript
{
    "field": "total",
    "header": "Итого", 
    "calc": "(e) => (e.qty || 0) * (e.price || 0)"
}
```

## Безопасность

- Используется `eval()` только для проверенных строк функций
- Проверяется наличие `=>` или `function` в строке
- При ошибке парсинга функция остается как строка
- Логируются предупреждения о неудачном парсинге

## Тестирование

Добавлен специальный тест `testCalcFields()` в `test-smart-editors.html`:
- Создает таблицу с различными типами calc функций
- Простые вычисления: `(e) => (e.qty || 0) * (e.price || 0)`
- Сложные вычисления с Math функциями
- Многострочные функции с блоками кода

## Результат

Теперь calc функции:
✅ Сохраняются при редактировании в JSON формате
✅ Сохраняются при редактировании в JavaScript формате  
✅ Корректно восстанавливаются после применения изменений
✅ Работают во всех поддерживаемых форматах
✅ Безопасно обрабатываются с проверкой ошибок

Проблема полностью решена - вычисляемые поля теперь корректно отображаются и редактируются в обоих типах редакторов шаблонов.
