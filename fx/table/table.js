import { FxElement, html, css } from '/fx.js';
import { $styles } from './table.x.js';
import '../button/button.js';
import '../selector/selector.js';
import '../selector1/selector1.js';
import '../rating/rating.js';
import '../info/info.js';

export class FxTable extends FxElement {
    static properties = {
        id: { type: String, notify: true },
        base: { type: Object },
        isReady: { type: Boolean },
        templateId: { type: String, default: '', save: true },
        options: { type: Object, default: {} },
        data: { type: Array, default: [] },
        filteredData: { type: Array, default: [] },
        columns: { type: Array, default: [] },
        headerHeight: { type: Number, default: 32 },
        rowHeight: { type: Number, default: 36 },
        footerHeight: { type: Number, default: 32 },
        visibleRows: { type: Array, default: [] },
        startIndex: { type: Number, default: 0 },
        endIndex: { type: Number, default: 0 },
        resizing: { type: Boolean, default: false },
        resizingColumn: { type: Object },
        startX: { type: Number },
        columnWidth: { type: Object, default: {} },
        saveColumnWidth: { type: Boolean, default: false, save: true },
        draggingColumn: { type: Object },
        dragOverIndex: { type: Number, default: -1 },
        searchText: { type: String, default: '' },
        searchColumns: { type: Array, default: [], save: true },
        searchMode: { type: String, default: 'any' },
        sortField: { type: String, default: '' },
        sortDirection: { type: String, default: 'none' }, // 'none', 'asc', 'desc'
        footerCalculations: { type: Object, default: {} }, // 'sum'|'count'|'avg'|'custom', customFn: Function
        selectedRowIndex: { type: Number, default: -1 },
        topLabel: { type: String, default: '' },
        bottomLabel: { type: String, default: '' },
        hide: { type: String, default: 'c' },
        draggable: { type: Boolean, default: false },
        showVerticalLines: { type: Boolean, default: false, save: true },
        mode: { type: String, default: '' },
        scrollW: { type: Number, default: 0, save: true },
        scrollH: { type: Number, default: 4, save: true }
    }
    get hideSearch() { return this.hide?.includes('s') }
    get hideNavigation() { return this.hide?.includes('n') }
    get hideTop() { return this.hide?.includes('t') }
    get hideBottom() { return this.hide?.includes('b') }
    get hideSets() { return this.hide?.includes('e') }
    get hideClearSearch() { return this.hide?.includes('c') }
    get selected() { return this.filteredData[this.selectedRowIndex] || this.data[this.selectedRowIndex] || null }

    firstUpdated() {
        super.firstUpdated();
        this.async(() => {
            this.style.setProperty('--scrollW', `${this.scrollW}px`);
            this.style.setProperty('--scrollH', `${this.scrollH}px`);
            document.body.style.opacity = 1;
            this.isReady = true;
            this.setupVirtualScroll();
            window.addEventListener('resize', () => this.updateVisibleRows());
            this.filteredData = [...(this.data || [])];

            this.setColumnWidth();

            // Применяем начальную сортировку, если указаны sortColumns
            if (this.options?.sortColumns?.length) {
                this.applySortColumns(this.options.sortColumns);
            }

            // Auto-load template if templateId is set
            if (this.templateId) {
                this.loadTemplate(this.templateId);
            }

            this.async(() => {
                this.updateVisibleRows();
            }, 100)
        }, 100)
    }

    'id-changed'(e) {
        this._updateSaves();
        this.setColumnWidth();
    }

    'templateId-changed'(e) {
        if (this.templateId && this.isReady) {
            this.loadTemplate(this.templateId);
        }
    }

    setColumnWidth() {
        this._columns = [];
        this.$update;
        this.async(() => {
            this.__saves ||= [];
            if (this.saveColumnWidth) {
                this.__saves.add('columnWidth');
                this._updateSaves();
                // const store = JSON.parse(localStorage.getItem(this._saveFileName));
                // this.columnWidth = store?.['fx-table.columnWidth'];
            } else {
                this.__saves.remove('columnWidth');
                this.columnWidth = {};
            }
            this._columns = undefined;
            this.async(() => {
                this.updateVisibleRows();
                if (this.isShowTableSets)
                    this.$fire('on_itemSelected', { item: this.dataTableSets(), label: 'Table settings ' + (this.mode || ''),  type: 'table-sets' });
            })
        }, 200)
    }

    // Метод для сортировки по нескольким столбцам
    applySortColumns(sortColumns) {
        this.filteredData = [...(this.data || [])];
        if (!sortColumns?.length) return;

        this.filteredData = [...this.filteredData].sort((a, b) => {
            for (const field of sortColumns) {
                let valueA = a[field] || 'яяя';
                let valueB = b[field] || 'яяя';

                if (typeof valueA === 'string') valueA = valueA.toLowerCase();
                if (typeof valueB === 'string') valueB = valueB.toLowerCase();

                if (valueA < valueB) return -1;
                if (valueA > valueB) return 1;
            }
            return 0;
        });

        // Обновляем индексы $idx после сортировки
        this.filteredData.forEach((row, index) => {
            if (this.columns.some(col => col.field === '$idx')) {
                row['$idx'] = index + 1;
            }
        });

        this.updateVisibleRows();
        this.$update();
    }

    updated(changedProperties) {
        super.updated(changedProperties);
        if (changedProperties.has('data') && this.data) {
            if (this.sortField && this.sortDirection !== 'none') {
                this.sortData(this.sortField, this.sortDirection);
            } else if (this.options?.sortColumns?.length) {
                this.applySortColumns(this.options.sortColumns);
            } else {
                this.filteredData = [...(this.data || [])];
            }
        }
        if (changedProperties.has('data') ||
            changedProperties.has('filteredData') ||
            changedProperties.has('footerCalculations')) {
            this.calculateFooterValues();
        }
        if (changedProperties.has('options') && this.options?.sortColumns?.length) {
            this.applySortColumns(this.options.sortColumns);
        }
    }

    setupVirtualScroll() {
        const tableBody = this.$qs('.body-scrollable');
        const headerScrollable = this.$qs('.header-scrollable');
        const footerScrollable = this.$qs('.footer-scrollable');
        if (tableBody && headerScrollable && footerScrollable) {
            tableBody.addEventListener('scroll', () => this.handleScroll(), { passive: true });
            headerScrollable.addEventListener('scroll', () => {
                if (!this._headerScrollRAF) {
                    this._headerScrollRAF = requestAnimationFrame(() => {
                        const scrollLeft = headerScrollable.scrollLeft;
                        tableBody.scrollLeft = scrollLeft;
                        footerScrollable.scrollLeft = scrollLeft;
                        this._headerScrollRAF = null;
                    })
                }
            }, { passive: true });
            footerScrollable.addEventListener('scroll', () => {
                if (!this._footerScrollRAF) {
                    this._footerScrollRAF = requestAnimationFrame(() => {
                        const scrollLeft = footerScrollable.scrollLeft;
                        tableBody.scrollLeft = scrollLeft;
                        headerScrollable.scrollLeft = scrollLeft;
                        this._footerScrollRAF = null;
                    })
                }
            }, { passive: true });
            this.updateVisibleRows();
        } else {
            this.async(() => this.setupVirtualScroll(), 100);
        }
    }
    handleScroll() {
        const scrollableBody = this.$qs('.body-scrollable');
        if (scrollableBody) {
            const scrollLeft = scrollableBody.scrollLeft;
            const headerScrollable = this.$qs('.header-scrollable');
            const footerScrollable = this.$qs('.footer-scrollable');
            if (headerScrollable) headerScrollable.scrollLeft = scrollLeft;
            if (footerScrollable) footerScrollable.scrollLeft = scrollLeft;
            this.updateVisibleRows();
        }
    }
    updateVisibleRows() {
        if (!this.filteredData || !this.isReady) return;
        const scrollableBody = this.$qs('.body-scrollable');
        if (!scrollableBody) return;
        const containerHeight = scrollableBody.clientHeight || 0;
        const visibleRowCount = Math.ceil(containerHeight / this.rowHeight) + 2;
        const scrollTop = scrollableBody.scrollTop || 0;
        this.startIndex = Math.floor(scrollTop / this.rowHeight);
        this.startIndex = Math.max(0, this.startIndex - visibleRowCount * 2);
        this.endIndex = Math.min(this.filteredData.length, this.startIndex + visibleRowCount * 4);
        this.visibleRows = this.filteredData.slice(this.startIndex, this.endIndex);
    }
    getColumnWidth(column) {
        if (!this.columnWidth?.[column.field] && column.width === 'auto') return 'auto';
        return +(this.columnWidth?.[column.field] || column.width || 150);
    }
    rowClick(e, row, rowIndex) {
        this.selectedRowIndex = this.selectedRowIndex === rowIndex ? -1 : rowIndex;
        this.fire('row-selected', { row, rowIndex, selected: this.selectedRowIndex === rowIndex, id: this.id });
        this.$fire('row-selected', { row, rowIndex, selected: this.selectedRowIndex === rowIndex, id: this.id });
        if (this.isShowTableSets)
            this.$fire('on_itemSelected', { item: this.dataTableSets(), label: 'Table settings ' + (this.mode || '') + ' cell - (' + this.selectedRowIndex + ')',  type: 'table-sets' });
    }
    loadfile(e) {
        const file = e.target.files[0];
        if (!file) return;
        const reader = new FileReader();
        reader.onload = (event) => {
            try {
                const loadedData = JSON.parse(event.target.result);
                // console.log("Loaded data:", loadedData);
                // this.data = loadedData;
                this.fire('loadNewData', { data: loadedData });
                this.$update();
            } catch (error) {
                console.error("Error parsing JSON:", error);
            }
        }
        reader.readAsText(file);
    }

    // Template management functions
    async saveTemplate(templateName) {
        if (!this.base?.dbLocal) {
            console.error('No database connection available');
            return false;
        }

        try {
            const templateId = 'table-template:' + FX.ulid();
            const templateDoc = {
                _id: templateId,
                type: 'table-template',
                name: templateName || 'Table Template',
                tableId: this.id,
                mode: this.mode,
                created: FX.dates().utc,
                columns: JSON.parse(JSON.stringify(this.columns)),
                footerCalculations: JSON.parse(JSON.stringify(this.footerCalculations)),
                options: JSON.parse(JSON.stringify(this.options))
            };

            await this.base.dbLocal.put(templateDoc);
            this.templateId = templateId;
            console.log('Template saved:', templateId);
            return templateId;
        } catch (error) {
            console.error('Error saving template:', error);
            return false;
        }
    }

    async loadTemplate(templateId) {
        if (!this.base?.dbLocal || !templateId) {
            return false;
        }

        try {
            const templateDoc = await this.base.dbLocal.get(templateId);
            if (templateDoc.type === 'table-template') {
                this.columns = JSON.parse(JSON.stringify(templateDoc.columns || []));
                this.footerCalculations = JSON.parse(JSON.stringify(templateDoc.footerCalculations || {}));
                this.options = JSON.parse(JSON.stringify(templateDoc.options || {}));
                this.templateId = templateId;
                this.$update();
                console.log('Template loaded:', templateId);
                return true;
            }
        } catch (error) {
            console.error('Error loading template:', error);
        }
        return false;
    }

    async getTemplatesList() {
        if (!this.base?.dbLocal) {
            return [];
        }

        try {
            const result = await this.base.dbLocal.allDocs({
                include_docs: true,
                startkey: 'table-template:',
                endkey: 'table-template:\ufff0'
            });

            return result.rows.map(row => ({
                id: row.doc._id,
                name: row.doc.name,
                created: row.doc.created,
                tableId: row.doc.tableId,
                mode: row.doc.mode
            }));
        } catch (error) {
            console.error('Error getting templates list:', error);
            return [];
        }
    }

    async deleteTemplate(templateId) {
        if (!this.base?.dbLocal || !templateId) {
            return false;
        }

        try {
            const doc = await this.base.dbLocal.get(templateId);
            await this.base.dbLocal.remove(doc);
            if (this.templateId === templateId) {
                this.templateId = '';
            }
            console.log('Template deleted:', templateId);
            return true;
        } catch (error) {
            console.error('Error deleting template:', error);
            return false;
        }
    }

    async showSets(e) {

        const run = (e, item) => {
            const id = item?.id;
            console.log(id)
            if (id === 'load') {
                this.$qs('#fileInput').click();
            }
            else if (id === 'save') {
                const dataStr = JSON.stringify(this.data, null, 2);
                const blob = new Blob([dataStr], { type: 'application/json' });
                const url = URL.createObjectURL(blob);

                const a = document.createElement('a');
                a.href = url;
                a.download = this.id + (this.mode ? '-' + this.mode : '') + ' ' + FX.dates().local + '.json';
                a.click();
                URL.revokeObjectURL(url);
            }
            else if (id === 'navigation') {
                if (this.hide.includes('n')){
                    this.hide = this.hide.replace('n', '');
                    this.hide = this.hide.replace('b', '');
                } else {
                    this.hide = this.hide + 'n';
                    this.hide = this.hide + 'b';
                }
                this.$update();
            }
            else if (id === 'scrollH') {
                this.scrollH = item.value;
                this.style.setProperty('--scrollH', `${this.scrollH}px`);
                this.$update();
            }
            else if (id === 'scrollW') {
                this.scrollW = item.value;
                this.style.setProperty('--scrollW', `${this.scrollW}px`);
                this.$update();
            }
            else if (id === 'saveColumnWidth') {
                this.saveColumnWidth = !this.saveColumnWidth;
                this.setColumnWidth();
            }
            else if (id === 'resetColumnWidth') {
                let status = this.saveColumnWidth;
                this.saveColumnWidth = true;
                this.columnWidth = {};
                this.saveColumnWidth = status;
                this.setColumnWidth();
            }
            else if (id === 'verticalLine') {
                this.showVerticalLines = !this.showVerticalLines;
            }
            else if (id === 'clearInfo') {
                this.selected.info = '';
                this.fire('change', {
                    cellChanged: true,
                    target: e.target,
                    value: '',
                    row: this.selected,
                    field: 'info',
                    rowIndex: this.selectedRowIndex
                })
                this.$update();
            }
            else if (id === 'templateEditor') {
                this.showTemplateEditor(e);
            }
            else if (id === 'saveTemplate') {
                this.showSaveTemplateDialog(e);
            }
            else if (id === 'loadTemplate') {
                this.showLoadTemplateDialog(e);
            }
        }
        const searchColumns = this.searchColumns?.length && this.searchColumns?.[0] ? this.searchColumns : this.columns.map(col => col.field);
        const item = this.dataTableSets = () =>  [
            {
                icon: 'material-symbols-light:select-check-box-rounded', label: 'current cell', subLabel: 'текущая ячейка', expanded: false, items: [
                    { id: 'clearInfo', icon: 'emojione-v1:circled-information-source', label: 'clear info', subLabel: 'очистить info', value: this.saveColumnWidth, value: 'clear info', is: 'button', run },
                ]
            },
            {
                icon: 'ph:columns-light', label: 'columns', subLabel: 'столбцы', expanded: false, items: [
                    { id: 'saveColumnWidth', label: 'save Column width', subLabel: 'сохранять ширину столбцов', value: this.saveColumnWidth, is: 'checkbox', run },
                    { id: 'resetColumnWidth', label: 'reset Column width', subLabel: 'сбросить ширину столбцов', value: 'reset', is: 'button', run },
                ]
            },
            {
                icon: 'material-symbols-light:swap-vert', label: 'data exchange', subLabel: 'обмен данными', items: [
                    { id: 'load', fill: 'blue', icon: 'la:file-upload', label: 'load data', subLabel: 'загрузить данные', value: 'загрузить', is: 'button', run },
                    { id: 'save', fill: 'red', icon: 'carbon:save', label: 'save data', subLabel: 'сохранить данные', value: 'сохранить', is: 'button', run },
                    // { id: 'load', fill: 'blue', icon: 'iconoir:multiple-pages', label: 'full load', subLabel: 'загрузить таблицу', value: 'загрузить все', is: 'button', run },
                    // { id: 'save-all', fill: 'red', icon: 'carbon:save-series', label: 'full save', subLabel: 'сохранить таблицу', value: 'сохранить все', is: 'button', run },
                ]
            },
            {
                icon: 'flat-color-icons:template', label: 'templates', subLabel: 'шаблоны таблицы', expanded: false, items: [
                    { id: 'templateEditor', icon: 'material-symbols-light:edit', label: 'template editor', subLabel: 'редактор шаблонов', value: 'edit', is: 'button', run },
                    { id: 'saveTemplate', icon: 'carbon:save', label: 'save template', subLabel: 'сохранить шаблон', value: 'save', is: 'button', run },
                    { id: 'loadTemplate', icon: 'la:file-upload', label: 'load template', subLabel: 'загрузить шаблон', value: 'load', is: 'button', run },
                ]
            },
            {
                icon: 'fx:settings', label: 'settings', subLabel: 'различные установки', value: ' id: ' + this.id, is: 'span', items: [
                    { label: 'switch Theme', subLabel: 'сменить тему', value: 'switch', is: 'button', run: () => { FX.switchTheme() } },
                    { id: 'verticalLine', label: 'show Vertical lines', subLabel: 'показывать вертикальные линии', value: this.showVerticalLines, is: 'checkbox', run },
                    { id: 'navigation', label: 'show Navigation', subLabel: 'показывать навигацию', value: !this.hide.includes('n'), is: 'checkbox', run },
                    { id: 'scrollH', label: 'set scroll height', subLabel: 'высота полосы прокрутки', value: this.scrollH, type: 'number', run },
                    { id: 'scrollW', label: 'set scroll width', subLabel: 'ширина полосы прокрутки', value: this.scrollW || '0', type: 'number', run },
                    // { label: 'search Columns', value: searchColumns.join(', '), is: 'input', run: (e) => { this.searchColumns = e.target.value.split(',').map(s => s.trim()) } },
                ]
            },
        ]
        this.isShowTableSets = true;
        await FX.showGrid({ type: 'table-sets', id: this.id + '-table-sets', item: item(), rowHeight: 32, hideSelected: true }, { label: 'Table settings ' + (this.mode || '') + ' cell - (' + this.selectedRowIndex + ')', parent: e.target, intersect: true, align: 'left', class: 'br', minWidth: 280, draggable: true, resizable: false, btnCloseOnly: true }, true);
        if (this.searchColumns.length === 1 && !this.searchColumns[0])
            this.searchColumns = [];
        this.isShowTableSets = false;
    }

    async showTemplateEditor(e) {
        const templateData = {
            columns: this.columns,
            footerCalculations: this.footerCalculations,
            options: this.options,
            templateId: this.templateId
        };

        const res = await FX.IO(templateData, {
            label: 'Template Editor',
            categories: ['properties'],
            showButtons: true
        }, {
            parent: e.target,
            align: 'modal',
            minWidth: 800,
            minHeight: 600
        });

        if (res?.detail?.obj) {
            const updatedData = res.detail.obj;
            this.columns = updatedData.columns || this.columns;
            this.footerCalculations = updatedData.footerCalculations || this.footerCalculations;
            this.options = updatedData.options || this.options;
            this.templateId = updatedData.templateId || this.templateId;
            this.$update();
        }
    }

    async showSaveTemplateDialog(e) {
        const res = await FX.showModal({
            ok: 'Save',
            cancel: 'Cancel',
            modal: '400, 200',
            label: 'Save Template',
            info1: 'Enter template name:',
            input: this.templateId ? 'Updated Template' : 'New Template'
        });

        if (res.detail === 'ok' && res.input) {
            const templateId = await this.saveTemplate(res.input);
            if (templateId) {
                await FX.showModal({
                    ok: 'Ok',
                    modal: '300, 160',
                    label: 'Success',
                    info1: 'Template saved successfully',
                    info2: `Template ID: ${templateId}`
                });
            } else {
                await FX.showModal({
                    ok: 'Ok',
                    modal: '300, 160',
                    label: 'Error',
                    info1: 'Failed to save template',
                    info2: 'Please try again'
                });
            }
        }
    }

    async showLoadTemplateDialog(e) {
        const templates = await this.getTemplatesList();

        if (templates.length === 0) {
            await FX.showModal({
                ok: 'Ok',
                modal: '300, 160',
                label: 'Info',
                info1: 'No templates found',
                info2: 'Create a template first'
            });
            return;
        }

        const items = templates.map(template => ({
            id: template.id,
            label: template.name,
            subLabel: `Created: ${FX.dates(template.created).local}`,
            icon: 'flat-color-icons:template',
            value: template.id,
            is: 'button',
            run: async (e, item) => {
                const success = await this.loadTemplate(item.value);
                if (success) {
                    await FX.showModal({
                        ok: 'Ok',
                        modal: '300, 160',
                        label: 'Success',
                        info1: 'Template loaded successfully',
                        info2: template.name
                    });
                } else {
                    await FX.showModal({
                        ok: 'Ok',
                        modal: '300, 160',
                        label: 'Error',
                        info1: 'Failed to load template',
                        info2: 'Please try again'
                    });
                }
            }
        }));

        await FX.showGrid({
            id: this.id + '-load-template',
            item: items,
            rowHeight: 32,
            hideSelected: true
        }, {
            parent: e.target,
            label: 'Load Template',
            intersect: true,
            align: 'left',
            class: 'br',
            minWidth: 400,
            draggable: true,
            resizable: false,
            btnCloseOnly: true
        });
    }

    static styles = [
        $styles.table,
        css`
            .vertical-line {
                border-right: 1px solid var(--fx-border-color3, #ccc);
                /* box-sizing: border-box; */
            }
            .vertical-line-hide {
                border-right: 1px solid transparent;
                /* box-sizing: border-box; */
            }
        `
    ]

    renderTop() {
        if (this.hideTop) return null;
        return html`
            <div class="top m2 horizontal w100 relative center wrap">
                <div class="top-label p4 pl4" ?hidden=${!this.topLabel}>${this.topLabel}</div>
                <div class="flex">
                    <slot name="top"></slot>
                </div>
                <div class="horizontal center relative">    
                    <div class="top-search p4 pr8 horizontal center relative" ?hidden=${this.hideSearch}>
                        <input id="search-input" class="flex mr4" type="search" placeholder="Search ..."
                            @input=${e => this.search(e.target.value)}
                            .value=${this.searchText || ''}
                            style="width: 160px; height: 24px; border: 1px solid var(--fx-border-color, #ccc); border-radius: 4px; padding: 0 6px; outline: none;">
                        <div class="horizontal center mr4">
                            <select id="search-mode" @change=${e => {
                this.searchMode = e.target.value;
                if (this.searchText) this.search(this.searchText, [], this.searchMode);
            }}
                                .value=${this.searchMode}
                                style="border: 1px solid var(--fx-border-color, #ccc); border-radius: 4px; height: 24px; outline: none; font-size: 12px;"
                        >
                                <option>any</option>
                                <option>all</option>
                                <option>exact</option>
                            </select>
                        </div>
                        <fx-icon class="but" url="fx:close:22" scale=.8 an="btn" br="square" @click=${this.clearSearch} ?hidden=${this.hideClearSearch} ></fx-icon>
                    </div>
                    <fx-icon class="but mr9" url="bx:dots-vertical-rounded:22" scale=.8 an="btn" br="square" @click=${this.showSets} ?hidden=${this.hideSets} ></fx-icon>
                </div>
            </div>
        `
    }
    renderBottom() {
        if (this.hideBottom) return null;
        return html`
            <div class="bottom horizontal w100 relative center wrap">
                <div class="bottom-label p4 pl4" ?hidden=${!this.bottomLabel}>${this.bottomLabel}</div>
                <div class="flex w100">
                    <slot name="bottom"></slot>
                </div>
                ${this.renderNavigationButtons()}
            </div>
        `
    }
    renderNavigationButtons() {
        if (this.hideNavigation) return html``;
        if (!this.filteredData || this.filteredData.length === 0) return html``;
        const currentPosition = this.selectedRowIndex >= 0 ? this.selectedRowIndex : this.startIndex;
        const totalRows = this.filteredData.length;
        return html`
            <div class="bottom-nav p4 pr4 horizontal center relative overflow-x">
                <div class="navigation-buttons horizontal">
                    <button class="nav-button" @click=${() => this.scrollToRow(0)} ?disabled=${currentPosition === 0}> 0 </button>
                    ${totalRows > 100000 ? html`
                        <button class="nav-button" @click=${() => this.scrollToRow(Math.max(0, currentPosition - 100000))} ?disabled=${currentPosition < 100000}>-100k</button>
                    ` : ''}
                    ${totalRows > 10000 ? html`
                        <button class="nav-button" @click=${() => this.scrollToRow(Math.max(0, currentPosition - 10000))} ?disabled=${currentPosition < 10000}>-10k</button>
                    ` : ''}
                    ${totalRows > 1000 ? html`
                        <button class="nav-button" @click=${() => this.scrollToRow(Math.max(0, currentPosition - 1000))} ?disabled=${currentPosition < 1000}>-1k</button>
                    ` : ''}
                    ${totalRows > 100 ? html`
                        <button class="nav-button" @click=${() => this.scrollToRow(Math.max(0, currentPosition - 100))} ?disabled=${currentPosition < 100}>-100</button>
                    ` : ''}
                    <input type="number" class="current-position-input mr2 ml2" min="1" max="${totalRows}" .value="${currentPosition + 1}"
                        @change=${(e) => {
                const newPosition = parseInt(e.target.value, 10) - 1;
                if (!isNaN(newPosition) && newPosition >= 0 && newPosition < totalRows) {
                    this.scrollToRow(newPosition);
                } else {
                    e.target.value = currentPosition + 1;
                }
            }
            }>
                    ${totalRows > 100 ? html`
                        <button class="nav-button" @click=${() => this.scrollToRow(Math.min(totalRows - 1, currentPosition + 100))} ?disabled=${currentPosition + 100 >= totalRows}>+100</button>
                    ` : ''}
                    ${totalRows > 1000 ? html`
                        <button class="nav-button" @click=${() => this.scrollToRow(Math.min(totalRows - 1, currentPosition + 1000))} ?disabled=${currentPosition + 1000 >= totalRows}>+1k</button>
                    ` : ''}
                    ${totalRows > 10000 ? html`
                        <button class="nav-button" @click=${() => this.scrollToRow(Math.min(totalRows - 1, currentPosition + 10000))} ?disabled=${currentPosition + 10000 >= totalRows}>+10k</button>
                    ` : ''}
                    ${totalRows > 100000 ? html`
                        <button class="nav-button" @click=${() => this.scrollToRow(Math.min(totalRows - 1, currentPosition + 100000))} ?disabled=${currentPosition + 100000 >= totalRows}>+100k</button>
                    ` : ''}
                    <button class="nav-button" @click=${() => this.scrollToRow(totalRows - 1)} ?disabled=${currentPosition === totalRows - 1}>${totalRows}</button>
                </div>
            </div>
        `
    }

    renderHeaderCell(column, index) {
        const minWidth = column.minWidth || 0;
        const width = this.getColumnWidth(column);
        const widthStyle = width === 'auto' ? 'auto' : `${width}px`;
        const flexStyle = width === 'auto' ? 'flex: 1;' : '';
        const isDragging = this.draggingColumn && this.draggingColumn.index === index;
        const isDragOver = this.dragOverIndex === index;
        const isSorted = this.sortField === column.field;
        const sortClass = isSorted ?
            (this.sortDirection === 'asc' ? 'sorted-asc' :
                this.sortDirection === 'desc' ? 'sorted-desc' : '') : '';
        return html`
            <div class="header-cell ${isDragging ? 'dragging' : ''} ${isDragOver ? 'drag-over' : ''} ${sortClass}"
                 style="min-width: ${minWidth}px; width: ${widthStyle}; ${flexStyle}; ${column.headerStyle || this.options?.headerStyle || ''}"
                 draggable=${this.draggable}
                 @click="${() => this.toggleSort(column.field)}"
                 @dragstart="${(e) => this.startDrag(e, column, index)}"
                 @dragover="${(e) => this.handleDragOver(e, index)}"
                 @drop="${(e) => this.handleDrop(e, index)}">
                <div class="cell-content horizontal center">
                    ${column.header}
                    ${isSorted ? html`
                        <span class="sort-indicator" style="margin-top: -3px;">
                            ${this.sortDirection === 'asc' ? '↑' : this.sortDirection === 'desc' ? '↓' : ''}
                        </span>
                    ` : ''}
                </div>
                <div class="resizer" @pointerdown="${(e) => this.startResize(e, column)}"></div>
            </div>
        `
    }
    renderRow(row, rowIndex) {
        const isEven = rowIndex % 2 === 0;
        return html`
            <div class="table-row ${isEven ? 'even-row' : 'odd-row'} ${this.selectedRowIndex === rowIndex ? 'selected' : 'ns'}"
                 style="height: ${this.rowHeight}px; position: relative;" @click=${e => this.rowClick(e, row, rowIndex)}>
                ${this.columns.filter(i => !i.autoHide).map((column, columnIndex) => this.renderCell(row, column, columnIndex, rowIndex))}
            </div>
        `;
    }
    renderCell(row, column, columnIndex, rowIndex) {
        const minWidth = column.minWidth || 0;
        const width = this.getColumnWidth(column);
        const widthStyle = width === 'auto' ? 'auto' : `${width}px`;
        const flexStyle = width === 'auto' ? 'flex: 1;' : '';

        // Если это столбец $idx, обновляем значение в данных
        if (column.field === '$idx') {
            row[column.field] = rowIndex + 1;
        }

        const verticalLineClass = this.showVerticalLines && columnIndex >= 0 ? 'vertical-line' : 'vertical-line-hide';

        return html`
            <div class="table-cell ${this.selectedRowIndex === rowIndex ? 'selected-cell' : ''} ${verticalLineClass}" 
                 style="min-width: ${minWidth}px; width: ${widthStyle}; ${flexStyle}">
                <fx-table-cell class="w100" .value=${row[column.field]} .field=${column.field} .row=${row} .column=${column}
                    .data=${this.data} .columnIndex=${columnIndex} .rowIndex=${rowIndex} ?selected=${this.selectedRowIndex === rowIndex} .table=${this}></fx-table-cell>
            </div>
        `;
    }
    renderFooterCell(column) {
        const minWidth = column.minWidth || 0;
        const width = this.getColumnWidth(column);
        const widthStyle = width === 'auto' ? 'auto' : `${width}px`;
        const flexStyle = width === 'auto' ? 'flex: 1;' : '';
        const footerValues = this.calculateFooterValues();
        const calculatedValue = footerValues[column.field];
        const content = calculatedValue !== undefined
            ? calculatedValue
            : (column.footer || '');
        return html`
            <div class="footer-cell  ${column.footerClass || ''}" style="min-width: ${minWidth}px; width: ${widthStyle}; ${flexStyle}; ${column.footerStyle || ''}">
                ${content}
            </div>
        `;
    }
    render() {
        if (!this.isReady) return html``;
        return html`
            <div class="table-container">
                ${this.renderTop()}
                <div class="table-header ${this.options?.headerClass || ''}" style="height: ${this.headerHeight}px; ${this.options?.headerStyle || ''}">
                    <div class="header-scrollable brt">
                        <div style="display: flex; width: 100%;">
                            ${(this._columns || this.columns || []).filter(i => !i.autoHide).map((column, index) => this.renderHeaderCell(column, index))}
                        </div>
                    </div>
                </div>
                <div class="table-body">
                    <div class="body-scrollable">
                        <div class="virtual-scroller" style="height: ${this.filteredData.length * this.rowHeight}px;">
                            <div class="visible-rows" style="transform: translateY(${this.startIndex * this.rowHeight}px);">
                                ${(this._columns || this.visibleRows || []).map((row, index) => this.renderRow(row, this.startIndex + index))}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="table-footer ${this.options?.footerClass || ''}" style="height: ${this.footerHeight}px; ${this.options?.footerStyle || ''}">
                    <div class="footer-scrollable brb">
                        <div style="display: flex; width: 100%;">
                            ${(this._columns || this.columns || []).filter(i => !i.autoHide).map((column, index) => this.renderFooterCell(column, index))}
                        </div>
                    </div>
                </div>
                ${this.renderBottom()}
            </div>
            <input type="file" id="fileInput" accept=".json" style="display: none;" @change=${this.loadfile} />
        `;
    }

    startResize(e, column) {
        this.resizing = true;
        this.resizingColumn = column;
        this.startX = e.clientX;
        if (column.width === 'auto') {
            const headerCell = e.target.closest('.header-cell');
            if (headerCell) {
                this.initialWidth = headerCell.offsetWidth;
            } else {
                this.initialWidth = 150;
            }
        } else {
            this.initialWidth = this.getColumnWidth(column);
        }
        document.addEventListener('pointermove', this.handleResize);
        document.addEventListener('pointerup', this.stopResize);
        e.preventDefault();
    }
    handleResize = (e) => {
        if (!this.resizing) return;
        const delta = e.clientX - this.startX;
        const newWidth = Math.max(50, this.initialWidth + delta);
        this.columnWidth = {
            ...this.columnWidth,
            [this.resizingColumn.field]: newWidth
        };
        if (this.resizingColumn.width === 'auto') {
            const columnIndex = this.columns.findIndex(col => col.field === this.resizingColumn.field);
            if (columnIndex !== -1) {
                const updatedColumns = [...this.columns];
                updatedColumns[columnIndex] = {
                    ...updatedColumns[columnIndex],
                    width: newWidth
                }
                this.columns = updatedColumns;
            }
        }
        this.$update();
        e.stopPropagation();
    }
    stopResize = (e) => {
        if (!this.resizing) return;
        document.removeEventListener('pointermove', this.handleResize);
        document.removeEventListener('pointerup', this.stopResize);
        if (e) {
            e.stopPropagation();
            e.preventDefault();
        }
        this.async(() => { this.resizing = false })
    }

    startDrag(e, column, index) {
        this.draggingColumn = { column, index };
        e.dataTransfer.effectAllowed = 'move';
    }
    handleDragOver(e, index) {
        e.preventDefault();
        this.dragOverIndex = index;
    }
    handleDrop(e, index) {
        e.preventDefault();
        if (!this.draggingColumn) return;
        const { index: fromIndex } = this.draggingColumn;
        if (fromIndex === index) return;
        const newColumns = [...this.columns];
        const [removed] = newColumns.splice(fromIndex, 1);
        newColumns.splice(index, 0, removed);
        this.columns = newColumns;
        // console.log(this.columns)
        this.draggingColumn = null;
        this.dragOverIndex = -1;
    }

    search(text, columns = this.searchColumns || [], mode = this.searchMode) {
        mode ||= 'any';
        this.searchText = text;
        const searchColumns = columns.length && columns[0] ? columns : this.columns.map(col => col.field);
        if (!text) {
            this.filteredData = [...this.data];
        } else {
            const searchLower = text.toLowerCase();
            const searchWords = searchLower.split(/\s+/).filter(word => word.length > 0);
            this.filteredData = this.data.filter(row => {
                let cellsValue = '';
                searchColumns.map(field => {
                    cellsValue += row[field] + ' ';
                })
                cellsValue = cellsValue.toLowerCase();
                if (mode === 'exact') {
                    return cellsValue.includes(searchLower);
                } else if (mode === 'all') {
                    return searchWords.every(word => cellsValue.includes(word));
                } else {
                    return searchWords.some(word => cellsValue.includes(word));
                }
            })
        }
        this.startIndex = 0;
        this.endIndex = 0;
        this.updateVisibleRows();
        //this.$update();
    }
    clearSearch() {
        this.searchText = '';
        this.filteredData = [...this.data];
        this.updateVisibleRows();
        this.$update();
    }

    scrollToRow(rowIndex) {
        if (!this.isReady || !this.filteredData || rowIndex < 0 || rowIndex >= this.filteredData.length) return;
        const scrollableBody = this.$qs('.body-scrollable');
        if (scrollableBody) {
            const scrollPosition = rowIndex * this.rowHeight;
            scrollableBody.scrollTop = scrollPosition;
            this.updateVisibleRows();
            this.selectedRowIndex = rowIndex;
            this.$update();
        }
    }

    sortData(field, direction) {
        this.sortField = field;
        this.sortDirection = direction;
        if (direction === 'none') {
            this.filteredData = [...this.data];
        } else {
            this.filteredData = [...this.filteredData].sort((a, b) => {
                let valueA = a[field];
                let valueB = b[field];
                if (typeof valueA === 'string') valueA = valueA.toLowerCase();
                if (typeof valueB === 'string') valueB = valueB.toLowerCase();
                if (valueA < valueB) return direction === 'asc' ? -1 : 1;
                if (valueA > valueB) return direction === 'asc' ? 1 : -1;
                return 0;
            })
        }
        this.updateVisibleRows();
        this.$update();
        this.async(() => {
            const scrollableBody = this.$qs('.body-scrollable');
            if (scrollableBody) {
                scrollableBody.scrollTop = 0;
                this.startIndex = 0;
                this.updateVisibleRows();
            }
        })
    }
    toggleSort(field) {
        if (this.resizing || this.draggingColumn) return;
        let newDirection = 'asc';
        if (this.sortField === field) {
            if (this.sortDirection === 'asc') {
                newDirection = 'desc';
            } else if (this.sortDirection === 'desc') {
                newDirection = 'none';
            }
        }
        this.sortData(field, newDirection);
    }

    calculateFooterValues() {
        const result = {};
        if (!this.footerCalculations || !this.filteredData || this.filteredData.length === 0) {
            return result;
        }
        Object.entries(this.footerCalculations).forEach(([field, config]) => {
            const type = config.type || 'sum';
            switch (type) {
                case 'sum':
                    let col = this.columns.find(i => i.field === field);

                    result[field] = this.filteredData.reduce((sum, row) => {
                        const value = col.calc ? parseFloat(col.calc(row)) : parseFloat(row[field]);
                        return sum + (isNaN(value) ? 0 : value);
                    }, 0);
                    break;
                case 'count':
                    result[field] = this.filteredData.length;
                    break;
                case 'avg':
                    const validValues = this.filteredData
                        .map(row => parseFloat(row[field]))
                        .filter(value => !isNaN(value));

                    result[field] = validValues.length > 0
                        ? validValues.reduce((sum, value) => sum + value, 0) / validValues.length
                        : 0;
                    break;
                case 'custom':
                    if (typeof config.customFn === 'function') {
                        result[field] = config.customFn(this.filteredData, field);
                    }
                    break;
            }
            if (result[field] !== undefined && typeof result[field] === 'number') {
                if (config.format === 'integer') {
                    result[field] = Math.round(result[field]);
                } else if (config.format === 'currency') {
                    result[field] = result[field].toLocaleString('ru-RU', {
                        style: 'currency',
                        currency: 'RUB',
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2
                    });
                } else if (config.decimals !== undefined) {
                    result[field] = result[field].toFixed(config.decimals);
                }
            }
        })
        return result;
    }

    addRow(rowData = {}) {
        this.data.push(rowData);
        this.filteredData.push(rowData);
        if (this.columns.some(col => col.field === '$idx'))
            rowData['$idx'] = this.data.length;
        this.updateVisibleRows();
        this.$update();
        this.async(() => {
            this.scrollToRow(this.data.length - 1);
            this.selectedRowIndex = this.data.length - 1;
        }, 100)
        this.fire('row-added', { row: rowData, index: this.data.length - 1 });
        return rowData;
    }
    deleteRow(rowIndex = this.selectedRowIndex) {
        if (rowIndex < 0 || rowIndex >= this.filteredData.length) return false;
        const deletedRow = this.filteredData[rowIndex];
        this.filteredData.splice(rowIndex, 1);
        const dataIndex = this.data.findIndex(row => row === deletedRow);
        if (dataIndex !== -1)
            this.data.splice(dataIndex, 1);
        if (this.columns.some(col => col.field === '$idx')) {
            this.data.forEach((row, index) => {
                row['$idx'] = index + 1;
            })
        }
        if (this.selectedRowIndex >= this.filteredData.length)
            this.selectedRowIndex = this.filteredData.length - 1;
        this.updateVisibleRows();
        this.$update();
        this.fire('row-deleted', { row: deletedRow, index: rowIndex });
        return true;
    }
}
customElements.define('fx-table', FxTable)

customElements.define('fx-table-cell', class extends FxElement {
    static get properties() {
        return {
            data: { type: Object },
            field: { type: String },
            value: { type: String, default: '' },
            columnIndex: { type: Number },
            rowIndex: { type: Number },
            column: { type: Object },
            row: { type: Object },
            action: { type: Object },
            _action: { type: Object },
            selected: { type: Boolean },
            table: { type: Object },
            _uniqueKey: { type: String, reflect: false }
        }
    }
    get readOnly() { return this.column.field === '$idx' || this.column?.readOnly || this.data?.options?.readOnly }
    get opt() { return this.table.options }

    // updated(changedProps) {
    //     if (changedProps.has('row') || changedProps.has('field')) {
    //         this._uniqueKey = `${this.rowIndex}-${this.field}`;
    //         const colType = this.column?.typeColumn;
    //         if (colType === 'html' || colType === 'textarea' || colType === 'selector' || colType === 'selector1') {
    //             const element = this.$qs(
    //                 colType === 'html' ? 'html' :
    //                     colType === 'textarea' ? 'textarea' :
    //                         colType === 'selector' ? 'fx-selector' : 'fx-selector1'
    //             )
    //             if (element) element.value = this.value || '';
    //         }
    //     }
    // }


    // get _calc() {
    //     let res = this.column.calc(this.row);
    //     if (+res) return (+res).toFixed(2);
    //     return '';
    // }

    _changeValue(e) {
        let value = e.target.value || e.detail;
        // console.log(value);
        this.row[this.column.field] = value;
        this.fire('change', {
            cellChanged: true,
            target: e.target,
            value,
            row: this.row,
            field: this.column?.field,
            rowIndex: this.rowIndex
        })
        if (this.opt?.sortColumns?.length) {
            this.async(() => {
                this.table.applySortColumns(this.opt.sortColumns);
                this.$update();
                this.table.$update();
            })
        }
    }
    _click(e) {
        e.preventDefault();
        e.stopPropagation();
    }
    _click2(e) {
        if (this.column.field !== 'id' && (this.selected || e.target.localName.includes('but') || e.target.localName.includes('icon')) || e.target.localName === 'input') {
            e.preventDefault();
            e.stopPropagation();
        }
    }
    _dblClick(e) {
        this.table.fire('dblclick-cell', {
            row: this.row,
            cell: this,
            field: this.field,
            value: this.value,
            rowIndex: this.rowIndex,
            columnIndex: this.columnIndex
        })
        e.stopPropagation();
    }

    static styles = [$styles.cell]

    render() {
        const col = this.column,
            opt = this.opt;
        let value = col.field === '$idx' ? this.rowIndex + 1 : col.calc ? col.calc(this.row) : this.value;
        value = col.calc && typeof value === 'number' ? value.toFixed(2) : value;
        value = (col.calc && isNaN(value) ? '' : value);
        return html`
            <div class="cell horizontal flex w100 h100 relative"
                    .title=${col?.showTitle ? value : ''}
                    @dblclick=${this._dblClick}
                    data-row-index="${this.rowIndex}"
                    data-field="${this.field}"
                    style="color: ${this.selected ? 'white' : 'inherit'};
                    text-align: ${col.textAlign || 'left'};"
                    @click=${this._click2}
                >
                ${!col?.typeColumn && !opt?.typeColumn || col?.typeColumn === 'span' ? html`
                    <span class="w100 ${col.class || ''}" style="${col.style || ''}">${value || ''}</span>
                ` : col?.typeColumn === 'html' || opt?.typeColumn === 'html' ? html`
                    <div class="horizontal flex w100 ${col.class || ''}"
                        .style=${col.style || ''}
                        .key=${this._uniqueKey}
                        .innerHTML=${value}
                    ></div>
                ` : col?.typeColumn === 'textarea' || opt?.typeColumn === 'textarea' ? html`
                    <textarea class="${col.class || ''}"
                        style="color: ${this.selected ? 'white' : 'inherit'}; overflow: hidden; ${col.style || ''}"
                        .key=${this._uniqueKey}
                        .value=${value || ''}
                        @input=${this._changeValue}
                    ></textarea>
                ` : col?.typeColumn === 'img' || opt?.typeColumn === 'img' ? html`
                    ${value ? html`
                        <img class="${col.class || ''}"
                            .style=${col.style || ''}
                            src=${value}
                        />
                    ` : html``}
                ` : col?.typeColumn === 'select' ? html`
                    ${this.readOnly || !this.selected ? html`
                        <span class="w100 ${col.class || ''}" .style=${col.style}>${value || ''}</span>`
                    : html`
                        <select class="inp fm w100 ${col.class || ''}"
                            style="color: ${this.selected ? 'white' : 'inherit'}; ${col.style || ''}"
                            @change=${this._changeValue}
                            ?readonly=${this.readOnly}
                        >
                            ${(col.options || []).map(o => html` <option value=${o} .selected=${value === o} style="background: #ddd!important; color: #333!important;">${o}</option>`)}
                        </select>
                    `}
                ` : col?.typeColumn === 'list' ? html`
                    <input class="input w100 fm pl4 pr4 ${col.class || ''}" type="search"
                        .value=${value || ''}
                        style="color: ${this.selected ? 'white' : 'inherit'}; ${col.style || ''}"
                        list=${col.list}
                        @blur=${this._changeValue}
                        ?readonly=${this.readOnly}
                    >
                    <datalist id=${col.list}>${(col.datalist || []).sort().map(o => html`
                        <option value=${o}\>`)}
                    </datalist>
                ` : col.calc ? html`
                    ${this._calc}
                ` : col?.typeColumn === 'info'  ? html`
                    <fx-info class="${col.class || ''}"
                        style="${col.style || ''}"
                        .id=${value}
                        .base=${this.base}
                        ._id=${this.row._id}
                        @changeInfo=${this._changeValue}
                    ></fx-info>
                ` : col?.typeColumn === 'rating' ? html`
                    <fx-rating class="${col.class || ''}"
                        style="${col.style || ''}"
                        .args=${col.args}
                        .value=${value}
                        @change=${this._changeValue}
                        ?readOnly=${this.readOnly}
                        @click=${this._click}
                    ></fx-rating>
                ` : col?.typeColumn === 'selector' ? html`
                    <fx-selector class="${col.class || ''}"
                        style="width: inherit; ${col.style || ''}"
                        .args=${col.args}
                        size=${col.size || 16}
                        .value=${value}
                        @change=${this._changeValue}
                        ?readOnly=${this.readOnly}
                        @click=${this._click}
                    ></fx-selector>
                ` : col?.typeColumn === 'selector1' ? html`
                    <fx-selector1 class="${col.class || ''}"
                        style="width: inherit; ${col.style || ''}"
                        .args=${col.args}
                        size=${col.size || 16}
                        color1=${col.color1 || 'gold'}
                        .value=${value}
                        @change=${this._changeValue}
                        ?readOnly=${this.readOnly}
                        @click=${this._click}
                    ></fx-selector1>
                ` : col?.typeColumn === 'progress' ? html`
                    <progress class="${col.class || ''}"
                        .style=${col.style || ''}
                        .value=${value}
                    ></progress>
                ` : col?.typeColumn === 'progress2' ? html`
                    <fx-progress class="${col.class || ''}"
                        .style=${col.style || ''}
                        .value=${value}
                    ></fx-progress>
                ` : col?.typeColumn === 'input' || opt?.typeColumn === 'input' ? html`
                    ${this.readOnly || !this.selected ? html`
                        <span class="w100 ${col.class || ''}" .style=${col.style || ''}>${value || ''}</span>`
                    : html`
                        <input class="input w100 ${col.class || ''}" type=${col?.typeInput || 'text'}
                            style="color: white; min-height: ${this.table.rowHeight - 8}px!important; ${col.style || ''}; text-align: ${col.textAlign || 'left'};"
                            .value=${value || ''}
                            @blur=${this._changeValue}
                        >
                    `}
                ` : html`
                    <span class="${col.class || ''}" .style=${col.style || ''}>${value || ''}</span>
                `}  
            </div>
        `
    }
})
