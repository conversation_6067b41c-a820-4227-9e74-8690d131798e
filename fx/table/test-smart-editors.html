<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="color-scheme" content="light dark" />
    <title>Smart Template Editors Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
        }
        .info {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        fx-table {
            width: 100%;
            height: 400px;
            border: 1px solid #ccc;
            border-radius: 5px;
        }
        .test-button {
            background: #2196F3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
        }
        .test-button:hover {
            background: #1976D2;
        }
        .test-button.secondary {
            background: #FF9800;
        }
        .test-button.secondary:hover {
            background: #F57C00;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Smart Template Editors Test</h1>
        
        <div class="info">
            <h3>Тест умных редакторов шаблонов:</h3>
            <ol>
                <li>Используйте кнопки ниже для создания тестовых данных</li>
                <li>Откройте настройки таблицы (кнопка с шестеренкой)</li>
                <li>Найдите раздел "templates" (шаблоны таблицы)</li>
                <li>Выберите один из редакторов:
                    <ul>
                        <li><strong>"template editor (JSON)"</strong> - для редактирования в JSON формате</li>
                        <li><strong>"template editor (JS)"</strong> - для редактирования в JavaScript формате</li>
                    </ul>
                </li>
                <li>Отредактируйте код и нажмите "Apply"</li>
                <li>Проверьте, что изменения применились к таблице</li>
            </ol>
            <p><strong>Умная логика:</strong></p>
            <ul>
                <li><strong>Есть настройки:</strong> Редактируются текущие columns, footerCalculations, options</li>
                <li><strong>Нет настроек:</strong> Загружается базовый шаблон из example-template.js или создается дефолтный</li>
                <li><strong>JSON редактор:</strong> Простое редактирование структуры данных</li>
                <li><strong>JavaScript редактор:</strong> Расширенное редактирование с примерами</li>
            </ul>
        </div>

        <div>
            <h3>Тестовые сценарии:</h3>
            <button class="test-button" onclick="clearTable()">1. Очистить таблицу (нет настроек)</button>
            <button class="test-button" onclick="testBasicTemplate()">2. Базовые настройки</button>
            <button class="test-button" onclick="testAdvancedTemplate()">3. Расширенные настройки</button>
            <button class="test-button secondary" onclick="testComplexTemplate()">4. Сложные настройки с вычислениями</button>
            <button class="test-button secondary" onclick="testCalcFields()">5. Тест calc функций</button>
        </div>

        <fx-table id="testTable"></fx-table>
    </div>

    <script type="module">
        import './table.js';
        import '../base/base.js';

        // Инициализация базы данных
        const initDB = async () => {
            if (!window.FX.PouchDB) {
                await import('/fx/~/pouchdb/pouchdb-9.0.0.min.js');
                window.FX.PouchDB = PouchDB;
            }
            
            const dbLocal = new window.FX.PouchDB('test-smart-editors-db');
            
            // Создаем mock base объект
            const mockBase = {
                dbLocal: dbLocal
            };
            
            return mockBase;
        };

        // Инициализация таблицы
        const initTable = async () => {
            const table = document.getElementById('testTable');
            const base = await initDB();
            
            table.base = base;
            table.id = 'test-smart-table';
            table.mode = 'smart-test';
            
            // Пустая таблица для тестирования
            table.data = [];
            table.columns = [];
            table.footerCalculations = {};
            table.options = {};

            console.log('Table initialized for smart editors testing');
            
            // Делаем таблицу доступной глобально для тестов
            window.testTable = table;
        };

        // Тестовые функции
        window.clearTable = () => {
            const table = window.testTable;
            table.data = [];
            table.columns = [];
            table.footerCalculations = {};
            table.options = {};
            table.$update();
            console.log('Table cleared - no settings available');
        };

        window.testBasicTemplate = () => {
            const table = window.testTable;
            table.columns = [
                { field: 'id', header: 'ID', width: 64 },
                { field: 'name', header: 'Name', width: 150, typeColumn: 'input' }
            ];
            table.data = [
                { id: 1, name: 'Test Item 1' },
                { id: 2, name: 'Test Item 2' }
            ];
            table.footerCalculations = { id: { type: 'count' } };
            table.options = {};
            table.$update();
            console.log('Basic template applied - editors will edit current settings');
        };

        window.testAdvancedTemplate = () => {
            const table = window.testTable;
            table.columns = [
                { field: 'id', header: 'ID', width: 64, textAlign: 'center' },
                { field: 'name', header: 'Название', width: 150, typeColumn: 'input' },
                { field: 'price', header: 'Цена', width: 100, typeColumn: 'input', textAlign: 'right' },
                { field: 'qty', header: 'Кол-во', width: 80, typeColumn: 'input', textAlign: 'right' },
                { field: 'total', header: 'Итого', width: 120, calc: '(e) => (e.price || 0) * (e.qty || 0)', textAlign: 'right' }
            ];
            table.data = [
                { id: 1, name: 'Товар 1', price: 100, qty: 2 },
                { id: 2, name: 'Товар 2', price: 250, qty: 1 },
                { id: 3, name: 'Товар 3', price: 75, qty: 3 }
            ];
            table.footerCalculations = { 
                id: { type: 'count' },
                price: { type: 'avg' },
                qty: { type: 'sum' },
                total: { type: 'sum' }
            };
            table.options = {
                headerStyle: 'background: #2196F3; color: white;'
            };
            table.$update();
            console.log('Advanced template applied - editors will edit current settings');
        };

        window.testComplexTemplate = () => {
            const table = window.testTable;
            table.columns = [
                { field: 'id', header: 'ID', width: 50, textAlign: 'center' },
                { field: 'product', header: 'Товар', width: 200, typeColumn: 'input' },
                { field: 'category', header: 'Категория', width: 120, typeColumn: 'selector', dataList: ['Электроника', 'Одежда', 'Книги'] },
                { field: 'price', header: 'Цена', width: 80, typeColumn: 'input', textAlign: 'right' },
                { field: 'discount', header: 'Скидка %', width: 80, typeColumn: 'input', textAlign: 'right' },
                { field: 'finalPrice', header: 'Итоговая цена', width: 120, calc: (e) => (e.price || 0) * (1 - (e.discount || 0) / 100), textAlign: 'right', style: 'font-weight: bold; color: #4CAF50;' },
                { field: 'inStock', header: 'В наличии', width: 80, typeColumn: 'checkbox', textAlign: 'center' },
                { field: 'notes', header: 'Примечания', width: 200, typeColumn: 'textarea' }
            ];
            table.data = [
                { id: 1, product: 'Смартфон', category: 'Электроника', price: 25000, discount: 10, inStock: true, notes: 'Новая модель' },
                { id: 2, product: 'Рубашка', category: 'Одежда', price: 2500, discount: 20, inStock: true, notes: 'Хлопок 100%' },
                { id: 3, product: 'Учебник JS', category: 'Книги', price: 1200, discount: 0, inStock: false, notes: 'Ожидается поставка' }
            ];
            table.footerCalculations = { 
                id: { type: 'count' },
                price: { type: 'avg' },
                finalPrice: { type: 'sum' }
            };
            table.options = {
                headerClass: 'complex-header',
                footerClass: 'complex-footer',
                headerStyle: 'background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; font-weight: bold;',
                footerStyle: 'background: #f8f9fa; border-top: 2px solid #dee2e6; font-weight: bold;'
            };
            table.$update();
            console.log('Complex template applied - editors will edit current settings');
        };

        window.testCalcFields = () => {
            const table = window.testTable;
            table.columns = [
                { field: 'id', header: 'ID', width: 50, textAlign: 'center' },
                { field: 'name', header: 'Товар', width: 150, typeColumn: 'input' },
                { field: 'qty', header: 'Кол-во', width: 80, typeColumn: 'input', textAlign: 'right' },
                { field: 'price', header: 'Цена', width: 100, typeColumn: 'input', textAlign: 'right' },
                {
                    field: 'total',
                    header: 'Сумма',
                    width: 120,
                    calc: (e) => (e.qty || 0) * (e.price || 0),
                    textAlign: 'right',
                    style: 'font-weight: bold; color: #2196F3;'
                },
                {
                    field: 'tax',
                    header: 'НДС (20%)',
                    width: 100,
                    calc: (e) => Math.round(((e.qty || 0) * (e.price || 0)) * 0.2 * 100) / 100,
                    textAlign: 'right',
                    style: 'color: #FF5722;'
                },
                {
                    field: 'totalWithTax',
                    header: 'Итого с НДС',
                    width: 130,
                    calc: (e) => {
                        const total = (e.qty || 0) * (e.price || 0);
                        const tax = Math.round(total * 0.2 * 100) / 100;
                        return Math.round((total + tax) * 100) / 100;
                    },
                    textAlign: 'right',
                    style: 'font-weight: bold; color: #4CAF50; background: #E8F5E8;'
                }
            ];
            table.data = [
                { id: 1, name: 'Товар А', qty: 2, price: 100 },
                { id: 2, name: 'Товар Б', qty: 5, price: 250 },
                { id: 3, name: 'Товар В', qty: 1, price: 1500 },
                { id: 4, name: 'Товар Г', qty: 3, price: 75 }
            ];
            table.footerCalculations = {
                id: { type: 'count' },
                qty: { type: 'sum' },
                total: { type: 'sum' },
                tax: { type: 'sum' },
                totalWithTax: { type: 'sum' }
            };
            table.options = {
                headerStyle: 'background: #673AB7; color: white; font-weight: bold;',
                footerStyle: 'background: #F3E5F5; font-weight: bold; border-top: 2px solid #9C27B0;'
            };
            table.$update();
            console.log('Calc fields test applied - check that calc functions are preserved in editors');
        };

        // Запуск инициализации
        document.addEventListener('DOMContentLoaded', () => {
            initTable().catch(console.error);
        });
    </script>
</body>
</html>
