import { FxElement, html } from '/fx.js';
import { $styles, $btnsMT, $icons } from './base.x.js';
import { BS_ITEM } from './src/base-item.js';
import '../main/main.js';
import '../dbs/dbs.js';
import '../tree/tree.js';
import '../table/table.js';
import '../jupyter/jupyter.js';
import '../desk/desk.js';

export class FxBase extends FxElement {
    static properties = {
        dbPrefix: { type: String, default: 'aka-' },
        fxId: { type: String, default: '$fx-tree:000000' },
        fxItems: { type: Object },
        fxAll: { type: Array },
        fxFlat: { type: Object },
        fxSelected: { type: Object },
        isItem: { type: Object },
        // bs - BS_ITEM
        bsFlatItems: { type: Object, default: {} },
        bsFlatDrafts: { type: Object, default: {} },
        bsSelected: { type: Object },
        // any - item
        notebook: { type: Object },
        addNotebooks: { type: Array, default: [] },
        tableItems: { type: Object },
        tablePersons: { type: Object },
        hideDBS: { type: Boolean, default: true },
        lastLZS: { type: String, default: '', save: true },
        showLoader: { type: Boolean, default: false },
        toDeleteKeys: { type: Array, default: [] },
        changesMap: { type: Object },
        showConstructor: { type: Boolean },
    }
    get BS_ITEM() { return BS_ITEM }
    get main() { return this.$qs('fx-main') }
    get tree() { return this.$qs('fx-tree') }

    get fxAll() { return BS_UTILS.allItems(this.fxItems) || [] }
    get fxFlat() { return BS_UTILS.flatItems(this.fxItems, true) || {} }
    get fxFlatLength() { return Object.keys(this.fxFlat || {}).length }

    get bsSelected() { return this.bsFlatItems?.[this.fxSelected._id] }

    get fxToDelete() { return this.fxAll?.filter(i => i._deleted) }
    get fxHasDeleted() { return this.fxToDelete.length || this.toDeleteKeys?.length }
    get changesMap() { return BS_ITEM.changesMap }
    get needSave() { return this.changesMap?.size > 0 || this.fxHasDeleted || this.attachments?.length }

    get dbs() { return this.$qs('fx-dbs') }

    get tabs() {
        let tabs = {
            id: this.id + '-tabs', hideClose: true, noDraggable: true,
            tabs: [
                {
                    id: 'desktop', icon: 'twemoji:desktop-computer', title: 'desktop',
                },
                {
                    id: 'info', icon: 'twemoji:information', title: 'info',
                },
            ]
        }
        if (this.fxSelected?.hideInfo) {
            tabs.tabs.pop();
        }
        if (this.fxSelected?.is === 'money') {
            import('../jmoney/jmoney.js');
            tabs.tabs.push({ id: 'money', icon: 'flat-color-icons:currency-exchange', title: 'money', group: 'money' });
        }
        if (this.fxSelected?.is === 'moneyTotal') {
            import('../jmoney/jmoney.js');
            tabs.tabs.push({ id: 'money-total', icon: 'noto-v1:money-bag', title: 'money total', width: 110, group: 'money-total' });
        }
        if (this.fxSelected?.is === 'man' || this.fxSelected?.is === 'woman') {
            import('../persona/persona.js');
            import('../family-tree/family-tree.js');
            tabs.tabs.push({ id: 'persona', icon: 'carbon:pedestrian-family', title: 'persona', group: 'persona' });
            tabs.tabs.push({ id: 'family-tree', icon: 'game-icons:family-tree', title: 'family tree', group: 'persona' });
        }
        if (this.fxSelected?.is === 'hlsDay') {
            import('../hls-day/hls-day.js');
            tabs.tabs.push({ id: 'hls-day', icon: 'flat-color-icons:sports-mode', title: 'hls day', group: 'hls' });
        }
        if (this.fxSelected?.is === 'hlsTotal') {
            import('../hls-total/hls-total.js');
            tabs.tabs.push({ id: 'hls-total', icon: 'fc-combo_chart', title: 'hls total', group: 'hls' });
        }
        if (this.fxSelected?.if?.ifEvents) {
            import('../events/events.js');
            tabs.tabs.push({ id: 'events', icon: 'flat-color-icons:overtime', title: 'events', group: 'events' });
        }
        if (this.fxSelected?.if?.ifCalendar) {
            import('../calendar/calendar.js');
            tabs.tabs.push({ id: 'calendar', icon: 'flat-color-icons:planner', title: 'calendar', group: 'calendar' });
        }
        if (this.fxSelected?.if?.ifForm) {
            import('../ftmp/ftmp.js');
            tabs.tabs.push({ id: 'form', icon: 'flat-color-icons:template', title: 'form', group: 'form' });
        }
        if (this.addNotebooks?.length > 0) {
            this.addNotebooks.forEach((notebook, index) => {
                tabs.tabs.push({
                    id: `add-info-${index}`,
                    icon: 'emojione-v1:circled-information-source',
                    title: notebook.$label || `add-info-${index}`,
                    group: 'add-info',
                    showEvery: true,
                    closable: true,
                    width: 110
                })
            })
        }
        return tabs;
    }
    get shortLabel() { return this.fxSelected?.label.slice(0, 42) + (this.fxSelected?.label.length > 42 ? '...' : '') }
    get prefixid() { return this.dbLocal?.name?.replace('$', '') }

    get idInfo() {
        const activeTab = this.main?.fxTabs?.activeTabName;
        let id;
        if (activeTab && activeTab.startsWith('add-info')) {
            const tabIndex = parseInt(activeTab.split('-')[2]);
            const notebook = this.addNotebooks?.[tabIndex];
            id = notebook?._id || this.fxSelected?._id;
        } else {
            id = this.fxSelected?._id;
        }
        if (!id.startsWith('info:'))
            id = 'info:' + id;
        return id;
    }

    constructor() {
        super();
        FX.base = this;
        const separator = window.location.hash || window.location.search;
        this.hashParams = new URLSearchParams(separator.substring(1));
        const prefix = this.hashParams.get('pr')
        this.dbPrefix = localStorage.getItem('fx-base.dbPrefix');
        if (prefix)
            this.dbPrefix = prefix;
        this.dbPrefix ||= 'aka-';
        localStorage.setItem('fx-base.dbPrefix', this.dbPrefix);
        this.id = this.dbPrefix.replaceAll('$', '') + (this.id || 'fx-base');
        // console.log(this.id);
    }
    firstUpdated() {
        super.firstUpdated();
        this.async(async () => {
            let lzs = this.hashParams.get('lzs');
            if (lzs) {
                this.style.opacity = 1;
                this.showLoader = true;
                await this.getLZS(lzs);
                this.on_dbSelected(this.dbLocal);
            } else {
                this.hideDBS = false;
            }
            this.main._btnClick = this.on_btnClick.bind(this);
            this.async(() => {
                this.listen('tab-closed', async (e) => {
                    const tab = e?.detail.tab;
                    if (tab.id.startsWith('add-info')) {
                        const tabIndex = parseInt(tab.id.split('-')[2]);
                        if (this.addNotebooks && tabIndex >= 0 && tabIndex < this.addNotebooks.length) {
                            this.addNotebooks.splice(tabIndex, 1);
                            this.async(() => {
                                this.$update();
                                this.main.$update();
                            }, 10)
                        }
                    }
                    this.$update();
                })
                this.initTableItems();
                this.initTablePersons();
                this.style.opacity = 1;
                this.$update();
                this.listen('desk-item-added', (e) => {
                    this.$update();
                })
                this.listen('removed-deleted', (e) => {
                    this.toDeleteKeys ||= [];
                    this.toDeleteKeys.push(...e.detail.deletedItems);
                    this.$update();
                })
            }, 500)
        })
    }

    async on_dbSelected(e) {
        if (!e?.detail?.dbLocal?.name && !e?.name) return;
        this.dbLocal = e.detail?.dbLocal || e;
        await this.checkDb();
        try {
            this.bsFlatItems ||= {};
            const allDocs = await this.dbLocal.allDocs({ include_docs: true, startkey: 'item:', endkey: 'item:' + '\ufff0' });
            allDocs?.rows.map(i => {
                const doc = i.doc;
                const item = new BS_ITEM({ _id: doc._id, type: 'item', isLoad: true, doc });
                this.bsFlatItems[doc._id] = item;
            })
            this.fxItems = await this.buildTree();
            this.async(async () => {
                await this.getInfo();
                await this.getIs();
                this.initTableItems();
                this.initTablePersons();
                this.dbLabel = this.fxItems?.label || this.dbLocal.name || 'fx-base';
                this.$update();
            }, 20)
        } catch (err) { console.log(err) }
    }
    async checkDb(db = this.dbLocal) {
        if (!db) return;
        let tree;
        try {
            tree = await db.get(this.fxId);
        } catch (err) { }
        if (!tree) {
            tree = {
                _id: this.fxId,
                expanded: true,
                label: db.name,
                date_utc: FX.dates().utc,
                date_local: FX.dates().local,
                items: []
            }
            await db.put(tree);
        }
    }
    async buildTree(flat = this.bsFlatItems, _id = this.fxId, type = 'item', root) {
        let doc;
        try { doc = await this.dbLocal.get(_id) } catch (error) { }
        root ||= new BS_ITEM({ _id, isLoad: !!doc, type, doc: doc || {} });
        root.items = [];
        const orphaned = new BS_ITEM({ _id: type + ':$orphaned$', isLoad: true, type, doc: { label: '~~~' } });
        orphaned.items = [];
        Object.values(flat).forEach(i => i.items = []);
        Object.values(flat).forEach(i => {
            const id = i.doc.parentId;
            if (!id || id === '$root-item:000000' || id === _id) {
                root.items.push(i);
            } else {
                if (!flat[id]) {
                    orphaned.items.push(i);
                } else {
                    flat[id].items ||= [];
                    flat[id].items.push(i);
                }
            }
        })
        if (orphaned.items.length)
            root.items.push(orphaned);
        this.sortAndUpdateTree(root);
        root.expanded = true;
        return root;
    }
    sortAndUpdateTree(item) {
        if (item.items && item.items.length > 0) {
            item.items.sort((a, b) => {
                const sortA = a.sort || a.doc?.sort || 0;
                const sortB = b.sort || b.doc?.sort || 0;
                return sortA - sortB;
            })
            item.items.forEach((child, index) => {
                if (child.sort !== index) {
                    child.sort = index;
                }
                this.sortAndUpdateTree(child, item._id);
            })
        }
    }

    async getBsDraft(_id, _doc) {
        if (!_id) return;
        let type = _id.split(':')[0],
            draft = BS_ITEM.changesMap.get(_id) || null;
        draft ||= this.bsFlatDrafts?.[_id];
        if (!draft) {
            let doc;
            try { doc = await this.dbLocal.get(_id) } catch (err) { }
            draft = new BS_ITEM({ _id, type, isLoad: true, doc: doc || _doc || {} });
        }
        if (draft) {
            this.bsFlatDrafts ||= {};
            this.bsFlatDrafts[_id] = draft;
        }
        return draft;
    }

    async on_itemSelected(e) {
        let item = e?.detail?.item || this.fxFlat?.[e?.detail?.row?._id];
        if (!item) item = this.fxSelected;
        else this.fxSelected = item;
        this.async(() => {
            if (this.isShowTreeSets || this.isShowAddItem) {
                this.$update();
                const grid =  document.querySelectorAll('fx-grid')[0];
                grid.parentElement.label = item.label;
                BS_UTILS.allItems({ items: grid.item }).map(i => {
                    if (i.id === 'iconName') i.icon = item.icon;
                })
                grid?.$update();
            }
        }, 20)
        await this.getInfo();
        await this.getIs();
        this.async(async () => {
            let is = item?.is || 'info';
            if (is === 'man' || is === 'woman') is = 'persona';
            else if (item?.if?.ifEvents) is = 'events';
            else if (item?.if?.ifCalendar) is = 'calendar';
            is = is === 'hlsDay' ? 'hls-day' : is;
            is = is === 'hlsTotal' ? 'hls-total' : is;
            is = is === 'moneyTotal' ? 'money-total' : is;
            // this.main.fxTabs.selectTab('', is);
            if (is === 'calendar') this.fire('select-item-calendar', { item });
        }, 100)
    }
    async on_addItem(e, res) {
        const selected = e?.detail?.selected || e || this.fxSelected;
        let setItems = false,
            newItem = new BS_ITEM({ label: '...', isNew: true, type: 'item', parentId: selected._id });
        if (res?.type) {
            newItem.icon = res.icon;
            if (res.is)
                newItem.is = res.is;
            if (res.hideInfo)
                newItem.hideInfo = true;
            if (!res.inside && selected._id !== this.fxId) {
                newItem.parentId = selected.parentId;
                (this.fxFlat[newItem.parentId] || this.fxItems).items.push(newItem.doc);
                setItems = true;
            }
        } else {
            if (selected.if?.ifCalendar || selected.is === 'man' || selected.is === 'woman' || res) {
                if (!res) {
                    await this.showAddItem(e, selected);
                    res = {};
                    return;
                }
                newItem.icon = res.icon;
                if (res.value === 'calendar') {
                    newItem.if ||= {};
                    newItem.if.ifCalendar = true;
                } else if (res.value === 'man' || res.value === 'woman') {
                    newItem.is = res.value;
                }
                if (!res.inside && selected._id !== this.fxId) {
                    newItem.parentId = selected.parentId;
                    (this.fxFlat[newItem.parentId] || this.fxItems).items.push(newItem.doc);
                    setItems = true;
                }
            } else {
                if (selected.if?.ifForm) {
                    newItem.if ||= {};
                    newItem.if.ifForm = true;
                    newItem.parentId = selected.parentId;
                    newItem.icon = selected.icon;
                    newItem.hideInfo = selected.hideInfo;
                } else if (selected.is === 'money' || selected.is === 'moneyTotal') {
                    newItem.is = 'money';
                    newItem.icon = 'fc:currency-exchange';
                    newItem.label = FX.dates().monthStr;
                    if (selected.is === 'money') {
                        newItem.parentId = selected.parentId;
                        this.fxFlat[newItem.parentId].items.push(newItem.doc);
                        setItems = true;
                    }
                } else if (selected.is === 'hlsDay' || selected.is === 'hlsTotal') {
                    newItem.is = 'hlsDay';
                    newItem.icon = 'fc:sports-mode';
                    newItem.label = FX.dates().short;
                    if (selected.is === 'hlsDay') {
                        newItem.parentId = selected.parentId;
                        this.fxFlat[newItem.parentId].items.push(newItem.doc);
                        setItems = true;
                    }
                }
            }
        }
        if (res && !res?.value && !res?.type) return;
        if (res?.if) newItem.if = res.if;

        // Устанавливаем sort для нового элемента
        const parentItems = setItems ?
            (this.fxFlat[newItem.parentId] || this.fxItems).items :
            (selected.items ||= []);
        newItem.sort = parentItems.length;

        if (!setItems) {
            selected.items.push(newItem.doc);
        }

        this.bsFlatItems ||= {};
        this.bsFlatItems[newItem._id] = newItem;
        this.fxFlat ||= {};
        this.fxFlat[newItem._id] = newItem.doc;

        selected.expanded = !res || res?.inside;
        if (res && !res.noAddInfo)
            this.addInfo(newItem, selected._id, res);
        this.$update();
        this.async(() => {
            this.$update();
            this.main.$update();
        }, 20)
        return newItem.doc;
    }
    addInfo(newItem, parentId, res) {
        const _id = 'info:' + newItem._id;
        let doc = {
            _id,
            ulid: FX.ulid(),
            type: 'info',
            cells: [
                {
                    ulid: FX.ulid(),
                    type: 'jupyter_cell',
                    cell_type: res?.type || 'link',
                    cell_extType: res?.type || 'link',
                }
            ]
        }
        const bs = new BS_ITEM({ _id, parentId, type: 'info', doc });
        this.bsFlatDrafts ||= {};
        this.bsFlatDrafts[_id] = bs;
        if (!res) {
            this.notebook = bs.doc;
            this.notebook.$label = newItem.label;
            this.notebook.icon = newItem.icon;
            this.tree.selectById(newItem._id);
            this.async(() => {
                if (this.$qs('fx-jupyter')?.fxCells?.[0])
                    this.$qs('fx-jupyter').fxCells[0].toolbarVisible = res ? false : true;
            }, 300)
        }
        this.$update();
        this.async(() => {
            this.$update();
            this.main.$update();
        }, 20)
    }

    async getInfo(selected = this.fxSelected, db = this.dbLocal, nb = 'notebook') {
        let _id = selected?._id || selected;
        if (!_id || typeof _id !== 'string') return;
        // console.log(_id)
        if (!_id.startsWith('info:'))
            _id = 'info:' + _id;
        let itemInfo = BS_ITEM.changesMap.get(_id) || this.bsFlatDrafts[_id];
        if (!itemInfo) {
            let doc;
            try { doc = await db.get(_id) } catch (err) { }
            itemInfo = new BS_ITEM({ _id, type: 'info', isLoad: true, doc: doc || { parentId: selected._id } });
        }
        this.bsFlatDrafts ||= {};
        this.bsFlatDrafts[_id] = itemInfo;

        if (nb === 'addNotebook') {
            this.addNotebooks ||= [];
            const notebook = itemInfo.doc;
            notebook.$label = selected.label;
            notebook.icon = selected.icon;
            this.addNotebooks.push(notebook);
        } else {
            this[nb] = {};
            this[nb] = itemInfo.doc;
            this[nb].$label = selected.label;
            this[nb].icon = selected.icon;
        }

        this.$update();
        return itemInfo;
    }
    async getIs(selected = this.fxSelected, db = this.dbLocal) {
        if (!selected) return;
        let type = selected?.is;
        // console.log('itemIs - ', type);
        if (!type || type === 'hlsTotal') return;
        let _id = type + ':' + selected._id,
            item = BS_ITEM.changesMap.get(_id);
        if (!item) {
            let doc;
            try { doc = await db.get(_id) } catch (err) { }
            item = new BS_ITEM({ _id, type, isLoad: true, doc: (doc || { parentId: selected._id }) });
        }
        this.isItem = item.doc;
        this.$update();
    }

    initTableItems() {
        this.tableItems = { columns: [], data: [], calc: {} };
        let tableItems = {};
        tableItems.columns = [
            { header: 'label', field: 'name', width: 'auto', textAlign: 'left', alignItems: 'flex-start', showTitle: true, class: 'fs', style: 'text-wrap: auto;', footerClass: 'fs' },
            { header: 'tags', field: 'tags', width: 70, typeColumn: 'html', class: 'fxs', style: 'text-wrap: auto;' },
            { header: 'date', field: 'date', width: 66, textAlign: 'center', class: 'fxs', style: 'text-wrap: auto;' }
        ]
        let rows = [];
        Object.values(this.fxFlat || {}).map(i => {
            if (!i.hideInfo && i.is !== 'man' && i.is !== 'woman') {
                const row = { date: '' }, doc = i;
                try {
                    row.date = FX.dates(FX.ulidToDateTime(doc._id.split(':').at(-1))).local;
                } catch (error) { }
                row.name = doc.label;
                // row.tags = '#r' + (doc.rating || 0) + ' ' + (doc.tags || '');
                row.tags = `<span class="fxs hidden">${'#r' + (doc.rating || 0) + ' '}</span><span class="fxs">${doc.tags || ''}</span>`;
                row._id = doc._id;
                rows.push(row)
            }
        })
        rows.sort((a, b) => a.date < b.date ? 1 : -1);
        tableItems.data = rows;
        // tableItems.calc = { name: { type: 'count' } };
        this.tableItems = tableItems;
        this.$update();
        this.async(() => {
            this.$qs('#' + this.prefixid + '-items')?.updateVisibleRows();
        }, 100)
    }
    async initTablePersons() {
        this.tablePersons = { columns: [], data: [], calc: {} };
        let tablePersons = {};
        tablePersons.columns = [
            { header: 'persona', field: 'name', width: 'auto', textAlign: 'left', alignItems: 'flex-start', showTitle: true, class: 'fs', style: 'text-wrap: auto;', footerClass: 'fs' },
            { header: 'dates', field: 'dates', width: 66, textAlign: 'left', class: 'fxs', style: 'text-wrap: auto;' },
        ]
        let rows = [];
        let keys = [];
        Object.values(this.fxFlat || {}).map(i => {
            if (i.is === 'man' || i.is === 'woman') {
                let d1 = i.dateStart?.split('T')?.[0];
                d1 = d1 ? d1.split('-').reverse().join('.') : '';
                let d2 = i.dateEnd?.split('T')?.[0];
                d2 = d2 ? d2.split('-').reverse().join('.') : '';
                const date = d1 + ' ' + d2;
                keys.push(i._id);
                const row = {};
                row.dates = date;
                row.name = i.label;
                row._id = i._id;
                rows.push(row)
            }
        })
        rows.sort((a, b) => a.name > b.name ? 1 : -1);
        tablePersons.data = rows;
        // tablePersons.calc = { name: { type: 'count' } };
        this.tablePersons = tablePersons;
        this.$update();
        this.async(() => {
            this.$qs('#' + this.prefixid + '-persons')?.updateVisibleRows();
        }, 100)
    }

    treeRowDeleted(e) {
        this.toDeleteKeys ||= [];
        this.toDeleteKeys.push(...e.detail.deletedItems.map(i => i._id || i));
        this.$update();
    }

    async on_btnClick(e, id) {
        id ||= e.target?.id;
        // console.log(id);
        if (id === 'search') {
            this.initTableItems();
            this.$update();
            return;
        }
        if (id === 'persons') {
            this.initTablePersons();
            this.$update();
            return;
        }
        if (id === 'btn-refresh') {
            await this.tree?.storeTreeState(null, true);
            this.async(() => {
                document.location.reload();
            }, 100)
            return;
        }
        if (id === 'btn-settings') {
            console.log(id);
            return;
        }
        if (id === 'btn-tree-sets') {
            this.showSets(e);
        }
        if (id === 'btn-save') {
            this.save();
        }
    }

    static styles = [$styles]

    get leftPanel() {
        let opt = { footerStyle: 'border: none;' };
        let fxItems = this.fxItems?.items?.[0]?._id === '$root-item:000000' ? this.fxItems?.items?.[0] : this.fxItems;
        return html`
            <div slot="files" class="vertical flex overflow-h h100" style="background: var(--fx-background)">
                <fx-tree id=${this.prefixid + '-fxtree'} .item=${fxItems} .base=${this} @selected=${this.on_itemSelected} @add-item=${e => this.on_addItem(e)} @removed-deleted=${this.treeRowDeleted} remoteAddItem>
                    <div class="horizontal flex" slot="panel">
                        <div class="flex"></div>
                        <fx-icon id="btn-tree-sets" class="but ml8" url="mdi:dots-vertical" scale=.8 an="btn" br="square" @click=${this.on_btnClick}></fx-icon>
                    </div>    
                </fx-tree>
            </div>
            <div slot="search" class="vertical flex overflow-h h100" style="background: var(--fx-background)">
                <fx-table .id=${this.prefixid + '-items'} rowHeight=48
                    .columns=${this.tableItems?.columns}
                    .data=${this.tableItems?.data || []}
                    .base=${this}
                    @row-selected=${this.on_itemSelected}
                    footerHeight="1" .options=${opt}
                ></fx-table>
            </div>
            <div slot="persons" class="vertical flex overflow-h h100" style="background: var(--fx-background)">
                <fx-table .id=${this.prefixid + '-persons'} rowHeight=48
                    .columns=${this.tablePersons?.columns}
                    .data=${this.tablePersons?.data || []}
                    .base=${this}
                    @row-selected=${this.on_itemSelected}
                    footerHeight="1" .options=${opt}
                ></fx-table>
            </div>
        `
    }
    get rightPanel() {
        if (!this.hideDBS)
            return html`
                <div slot="right" class="vertical flex overflow-y h100" style="background: var(--fx-background)">
                    <fx-dbs .base=${this} @db-selected=${this.on_dbSelected}></fx-dbs>
                </div>
            `
    }
    get mainPanel() {
        return html`
            <fx-desk id=${this.prefixid + '-desk'} slot="desktop" .base=${this}></fx-desk>
            <fx-jupyter slot="info" class="vertical flex overflow h100" .notebook=${this.notebook} .base=${this} showBorder></fx-jupyter>
            ${this.addNotebooks?.map((notebook, index) => {
                const slotName = `add-info-${index}`;
                const elementId = `add-info-${index}`;
                return html`
                    <fx-jupyter id="${elementId}" slot="${slotName}" class="vertical flex overflow h100" .notebook=${notebook} .base=${this} showBorder></fx-jupyter>
                `
            })}
            ${this.main?.activeTabGroup === 'money' ? html`
                <fx-jmoney slot="money" class="vertical flex overflow-h h100" .cell=${this.isItem} .base=${this}></fx-jmoney>
            ` : ''}
            ${this.main?.activeTabGroup === 'money-total' ? html`
                <fx-jmoney slot="money-total" class="vertical flex overflow-h h100" .cell=${this.isItem} isTotal .base=${this}></fx-jmoney>
            ` : ''}
            ${this.main?.activeTabGroup === 'calendar' ? html`
                <fx-calendar slot="calendar" class="vertical flex overflow-h h100" start=0 end=0 simpleMode .base=${this}></fx-calendar>
            ` : ''}
            ${this.main?.activeTabGroup === 'persona' && this.main?.activeTabName === 'persona' ? html`
                <fx-persona slot="persona" class="vertical flex overflow-h h100" .base=${this} .item=${this.fxSelected}></fx-persona>
            ` : ''}
            ${this.main?.activeTabGroup === 'persona' && this.main?.activeTabName === 'family tree' ? html`
                <fx-family-tree slot="family-tree" class="vertical flex overflow-h h100" .base=${this} .item=${this.fxSelected}></fx-family-tree>
            ` : ''}
            ${this.main?.activeTabGroup === 'events' ? html`
                <fx-events slot="events" class="vertical flex overflow-h h100" .item=${this.fxSelected} .base=${this}></fx-events>
            ` : ''}
            ${this.main?.activeTabGroup === 'hls' ? html`
                <fx-hls-day slot="hls-day" class="vertical flex overflow-h h100" .item=${this.isItem} .base=${this}></fx-hls-day>
                <fx-hls-total slot="hls-total" class="vertical flex overflow-h h100" .base=${this}></fx-hls-total>
             ` : ''}
            ${this.main?.activeTabGroup === 'form' ? html`
                <fx-ftmp id=${this.id + '-form'} slot="form" class="vertical flex overflow-h h100" .item=${this.bsSelected} .base=${this} .showConstructor=${this.showConstructor}></fx-ftmp>
             ` : ''}
        `
    }
    render() {
        return html`
                <fx-main id=${this.id + '-main'} class="w100 h100 relative" hide=${this.hideDBS ? "fbr" : "fb"} overlay='r' .btnsMT=${$btnsMT} minL=164 minR=164 ?needSave=${this.needSave} .tabs=${this.tabs} tabsWidth=100 .base=${this}>
                    <label slot="top">${this.dbLabel || ''}</label>
                    ${this.leftPanel}
                    ${this.mainPanel}   
                    ${this.rightPanel}
                </fx-main>
                <div class="loader" ?hidden=${!this.showLoader}></div>
            `
    }

    async save() {
        if (!this.needSave) return;
        this.tree?.storeTreeState(null, true);
        const map = BS_ITEM.changesMap,
            changeKeys = Array.from(map.keys()),
            db = this.dbLocal;

        let deletedKeys = [...(this.toDeleteKeys || [])],
            rows = [],
            needReboot = false;
        this.toDeleteKeys = [];
        this.fxToDelete.map(i => deletedKeys.add(i._id));
        const ops = ['info', 'file', 'events', 'hlsDay', 'money', 'moneyTotal', 'field'];
        [...deletedKeys].map(i => ops.map(j => deletedKeys.add(j + ':' + i)));

        if (this.toDeleteAttachments?.length) {
            await Promise.all(this.toDeleteAttachments.map(async i => {
                let doc;
                try {
                    doc = await db.get(i._id);
                } catch (error) { }
                await db.removeAttachment(i._id, i.ulid, doc?._rev || null);
                doc = await db.get(i._id);
            }))
            this.toDeleteAttachments = [];
        }

        if (deletedKeys.length) {
            deletedKeys = deletedKeys.filter(i => i);
            await Promise.all(await deletedKeys.map(async i => {
                const allDocs = await db.allDocs({ include_docs: false, startkey: i, endkey: i + '\ufff0' });
                allDocs.rows.map(_i => {
                    if (_i.value?.rev) {
                        let doc = { _id: _i.id, _rev: _i.value.rev, _deleted: true };
                        rows.add(doc);
                    }
                })
            }))
            needReboot = true;
            // console.log(rows);
        }
        rows = rows.filter(i => i);
        if (rows.length) await db.bulkDocs(rows);

        let keys = [];
        rows = [];
        changeKeys.map(async i => {
            keys.add(i);
        })
        if (keys.length) {
            keys = keys.filter(i => i);
            const allDocs = await this.dbLocal.allDocs({ keys, include_docs: false });
            allDocs.rows.map(i => {
                let doc = map.get(i.key)?.doc;
                // doc = this.lzsInfo && doc?.type === 'info' ? map.get(i.key)?.lzsDoc : doc; // ToDo ???
                if (doc) {
                    doc = { ...doc };
                    if (doc.type === 'item')
                        delete doc.items;
                    delete doc.expanded;
                    delete doc.checked;
                    if (i.value?.rev)
                        doc._rev = i.value.rev;
                    rows.add(doc);
                }
            })
            rows = rows.filter(i => i);
            if (rows.length) await db.bulkDocs(rows);
        }
        map.clear();

        if (this.attachments?.length) {
            this.attachments.map(async attachment => {
                let i;
                try {
                    i = await db.get(attachment._id)
                } catch (error) { }
                if (i?._rev)
                    attachment._rev = i._rev;
                await db.put(attachment)
                this.attachments = undefined;
                this.$update();
            })
        }

        if (needReboot) {
            this.async(() => {
                document.location.reload();
            })
        }
        await this.tree?.restoreTreeState();
        this.async(() => {
            BS_ITEM.changesMap.clear();
            this.$update();
        }, 100)
        this.$update();
    }

    async showSets(e) {
        let res = { inside: true };
        const run = async (e, item) => {
            let id = item.id,
                spl = (item.icon2 || item.icon).split(':'),
                icon = spl[0] + (spl[1] ? ':' + spl[1] : '');
            // console.log(item);
            if (id === 'deleteIcon') {
                delete this.fxSelected.icon;
                delete this.fxSelected.iconSize;
            }
            else if (id === 'iconName') {
                this.fxSelected.icon = e.target.value;
            }
            else if (id === 'selectIcon') {
                let res = await FX.show('dropdown', '/icons/icons.js', { isSelectIcon: true }, {}, 'fx', 'fx');
                if (res?.detail?.res) {
                    let spl = res.detail.res.split(':'),
                        icon = spl[0] + (spl[1] ? ':' + spl[1] : '');
                    this.fxSelected.icon = icon;
                }
            }
            else if (id === 'sizeIcon') {
                this.fxSelected.iconSize = e.target.value;
            }
            else if (id === 'hideInfo') {
                this.fxSelected.hideInfo = item.value;
                this.tree.selectById(this.fxSelected._id);
            }
            else if (id === 'ifCalendar') {
                this.fxSelected.if ||= {};
                this.fxSelected.if.ifCalendar = item.value;
                if (!item.value && icon === this.fxSelected.icon)
                    delete this.fxSelected.icon;
                else
                    this.fxSelected.icon ||= icon;
            }
            else if (id === 'ifEvents') {
                this.fxSelected.if ||= {};
                this.fxSelected.if.ifEvents = item.value;
                if (!item.value && icon === this.fxSelected.icon)
                    delete this.fxSelected.icon;
                else
                    this.fxSelected.icon ||= icon;
            }
            else if (id === 'add-form') {
                res.icon = icon;
                res.type = 'form';
                res.hideInfo = true;
                res.if = { ifForm: true };
                if (!this.fxSelected.if?.ifForm)
                    res.if.ifConstructor = true;
                res.noAddInfo = true;
                await this.on_addItem('', res);
            }
            else if (id === 'ifForms') {
                this.fxSelected.if ||= {};
                this.fxSelected.if.ifForm = item.value;
                if (!item.value && icon === this.fxSelected.icon)
                    delete this.fxSelected.icon;
                else
                    this.fxSelected.icon ||= icon;
                if (this.fxSelected.if.ifForm) {
                    this.bsSelected.fields ||= { id: '$root-field', label: 'fields', items: [] };
                } else {
                    this.fxSelected.if.ifConstructor = false;
                }
            }
            else if (id === 'ifConstructor') {
                this.fxSelected.if ||= {};
                this.fxSelected.if.ifConstructor = item.value;
                if (this.fxSelected.if.ifConstructor) {
                    this.bsSelected.fields ||= { id: 'root-field', label: 'fields', items: [] };
                    this.fxSelected.if ||= {};
                    this.fxSelected.if.ifForm = true;
                    this.fxSelected.icon ||= 'flat-color-icons:template';
                }
            }
            else if (id === 'showConstructor') {
                this.showConstructor = !this.showConstructor;
                this.$update();
            }
            else if (id === 'deleteIs') {
                delete this.fxSelected.is;
                delete this.fxSelected.icon;
                delete this.fxSelected.iconSize;
            }
            else if (id.startsWith('is-')) {
                id = id.split('-')[1];
                if (item.value) {
                    this.fxSelected.is = id;
                    this.fxSelected.icon = icon;
                }
                else {
                    delete this.fxSelected.is;
                    delete this.fxSelected.icon;
                    delete this.fxSelected.iconSize;
                }
            }
            else if (id === 'showFX') {
                // FX.IO(this.fxSelected, { expert: true });
                FX.showJSON(this.fxSelected.doc, 'fx-item ' + this.fxSelected.label);
            }
            else if (id === 'showBS') {
                FX.showJSON(this.fxSelected, 'bs-item ' + this.fxSelected.label);
            }
            else if (id === 'export-tree') {
                this.exportTree();
            }
            else if (id === 'export-only-tree') {
                this.exportOnlyTree();
            }
            else if (id === 'import-tree') {
                this.importTree();
            }
            else if (id === 'inside') {
                res.inside = item.value;
            }
            else if (id.startsWith('add-cell-')) {
                id = id.split('-')[2];
                res.type = id;
                res.icon = icon;
                this.on_addItem('', res);
            }
            else if (id.startsWith('add-type-')) {
                id = id.split('-')[2];
                res.is = id;
                res.icon = icon;
                res.type = ' ';
                res.noAddInfo = true;
                this.on_addItem('', res);
            }
            else if (id === 'share-selected') {
                console.log(id)
            }
            else if (id === 'share-db') {
                let name = this.dbLocal.name,
                    lastLZS = FX.ulid(),
                    pr = this.dbPrefix,
                    href = location.href.split('#')[0],
                    dbUrl = this.dbs?.dbUrl || this.dbUrl,
                    hideDBS = true,
                    tree = this.tree._updateSaves(),
                    base = this._updateSaves(),
                    main = this.main._updateSaves(),
                    tabs = this.main.fxTabs._updateSaves(),
                    lzs = JSON.stringify({ name, dbUrl, pr, hideDBS, tree, base, main, tabs, lastLZS });
                lzs = FX.jcuri(lzs);
                href += '#pr=' + pr + '&lzs=' + lzs;
                window.open(href, '_blank')
            }
            else if (id.startsWith('adds')) {
                this[id](res.inside);
            }
            else if (id === 'addCalendar') {
                res.type = 'link';
                res.icon = icon;
                res.if = { ifCalendar: true };
                this.on_addItem('', res);
            }
            this.async(() => {
                if (this.isShowTreeSets)
                    this.on_itemSelected();
            }, 20)
            this.$update();
        }
        const item = this.dataTreeSets = () => [
            {
                id: 'addInfo', icon: 'flat-color-icons:data-recovery:28', label: 'add', subLabel: 'добавить', expanded: true, items: [
                    { id: 'inside', icon: 'fc:parallel-tasks:28', label: 'add inside', subLabel: 'добавлять внутрь', value: true, is: 'checkbox', run },
                    {
                        id: 'add-cell-link', icon: 'flat-color-icons:multiple-smartphones:28', label: 'add info', subLabel: 'добавить link', value: 'link', is: 'button', run, items: [
                            // { id: 'add-cell-link', icon: 'fc:survey:28', label: 'link', value: 'link', is: 'button', run },
                            { id: 'add-cell-html', icon: 'vscode-icons:file-type-html:28', label: 'html', value: 'html', is: 'button', run },
                            { id: 'add-cell-cherry-md', icon: 'emojione-v1:cherries:28', label: 'cherry-md', value: 'cherry-md', is: 'button', run },
                            { id: 'add-cell-galleries', icon: 'fc:multiple-cameras:28', label: 'galleries', value: 'galleries', is: 'button', run },
                            { id: 'add-cell-todo', icon: 'fc:todo-list:28', label: 'to-do', value: 'to-do', is: 'button', run },

                            {
                                id: 'add-cell-code', icon: 'devicon:vscode:28', label: 'code', value: 'code', is: 'button', run, items: [
                                    { id: 'add-cell-executable', icon: 'devicon:reactnative:28', label: 'executable', value: 'executable', is: 'button', run },
                                    { id: 'add-cell-diff', icon: 'flat-color-icons:process:28', label: 'diff', value: 'diff', is: 'button', run },
                                ]
                            },
                            {
                                id: 'add-cell-txt', icon: 'devicon-original:apachespark:28', icon2: 'flat-color-icons:kindle:28', label: 'special', value: 'txt', is: 'button', run, items: [
                                    { id: 'add-cell-doc', icon: 'vscode-icons:file-type-word:28', label: 'doc', value: 'doc', is: 'button', run },
                                    { id: 'add-cell-svg', icon: 'devicon-original:apachespark:28', label: 'svg', value: 'svg', is: 'button', run },
                                    { id: 'add-cell-excalidraw', icon: 'devicon-original:gitbook:28', label: 'excalidraw', value: 'excalidraw', is: 'button', run },
                                    { id: 'add-cell-money', icon: 'fc:currency-exchange:28', label: 'money', value: 'money', is: 'button', run },
                                    { id: 'add-cell-family-tree', icon: 'fc:conference-call:28', label: 'family-tree', value: 'family-tree', is: 'button', run },
                                ]
                            },
                            {
                                id: 'add-cell-table', icon: 'flat-color-icons:view-details:28', label: 'table', value: 'table', is: 'button', run, items: [
                                    { id: 'add-cell-jspreadsheet', icon: 'fc:grid:28', label: 'jspreadsheet', value: 'jspreadsheet', is: 'button', run },
                                    { id: 'add-cell-spreadsheet', icon: 'fc:data-sheet:28', label: 'spreadsheet', value: 'spreadsheet', is: 'button', run },
                                ]
                            }

                        ]
                    },
                    {
                        id: 'add types', icon: 'fluent-emoji-flat:bookmark-tabs:28', label: 'add types', subLabel: 'добавить тип', items: [
                            {
                                id: 'addCalendar', icon: 'flat-color-icons:conference-call:28', icon: 'flat-color-icons:planner:28', label: 'persona', subLabel: 'персоны', value: 'add calendar', is: 'button', run, items: [
                                    { id: 'add-calendar', icon: 'flat-color-icons:businessman:28', label: 'man', subLabel: 'мужчина', value: 'man', is: 'button', run },
                                    { id: 'add-type-woman', icon: 'flat-color-icons:businesswoman:28', label: 'woman', subLabel: 'женщина', value: 'woman', is: 'button', run },
                                    { id: 'addsParents', icon: 'fx:close:28', svg: $icons.parents, label: 'parents', subLabel: 'родители', value: 'parents', is: 'button', run },
                                    { id: 'addsCouples', icon: 'fx:close:28', svg: $icons.weddingCouple, label: 'couples', subLabel: 'семейная пара', value: 'couples', is: 'button', run }
                                ]
                            },
                            {
                                id: 'add-type-hlsDay', icon: 'flat-color-icons:sports-mode:28', label: 'HLS day', subLabel: 'ЗОЖ по дням', value: 'HLS day', is: 'button', run, items: [
                                    { id: 'add-type-hlsTotal', icon: 'flat-color-icons:combo-chart:28', label: 'HLS total', subLabel: 'ЗОЖ итого', value: 'HLS total', is: 'button', run },
                                ]
                            },
                            {
                                id: 'add-type-money', icon: 'fc:currency-exchange:28', label: 'money day', subLabel: 'деньги за месяц', value: 'money day', is: 'button', run, items: [
                                    { id: 'add-type-moneyTotal', icon: 'noto-v1:money-bag:28', label: 'money total', subLabel: 'деньги итого', value: 'money total', is: 'button', run },
                                ]
                            },
                        ],
                    },


                ]
            },
            {
                id: 'set is', icon: 'fluent-emoji-flat:bookmark-tabs:28', label: 'set is - type', subLabel: 'установить / сменить тип', expanded: false, items: [
                    { id: 'deleteIs', icon: 'fx:close:28', fill: 'red', label: 'delete type', subLabel: 'удалить тип', value: 'delete type', is: 'button', run },
                    {
                        id: 'isPersona', icon: 'flat-color-icons:conference-call:28', label: 'persona', subLabel: 'персоны', items: [
                            { id: 'is-man', icon: 'flat-color-icons:businessman:28', label: 'man', subLabel: 'мужчина', get: () => this.fxSelected.is === 'man', is: 'checkbox', run },
                            { id: 'is-woman', icon: 'flat-color-icons:businesswoman:28', label: 'woman', subLabel: 'женщина', get: () => this.fxSelected.is === 'woman', is: 'checkbox', run },
                        ]
                    },
                    {
                        id: 'isHLS', icon: 'twemoji:sports-medal:28', label: 'health', subLabel: 'ЗОЖ', items: [
                            { id: 'is-hlsTotal', icon: 'flat-color-icons:combo-chart:28', label: 'HLS total', subLabel: 'ЗОЖ итого', get: () => this.fxSelected.is === 'hlsTotal', is: 'checkbox', run },
                            { id: 'is-hlsDay', icon: 'flat-color-icons:sports-mode:28', label: 'HLS day', subLabel: 'ЗОЖ по дням', get: () => this.fxSelected.is === 'hlsDay', is: 'checkbox', run },
                        ]
                    },
                    {
                        id: 'isMoney', icon: 'fluent-emoji-flat:heavy-dollar-sign:28', label: 'money', subLabel: 'деньги', items: [
                            { id: 'is-moneyTotal', icon: 'noto-v1:money-bag:28', label: 'money total', subLabel: 'деньги итого', get: () => this.fxSelected.is === 'moneyTotal', is: 'checkbox', run },
                            { id: 'is-money', icon: 'fc:currency-exchange:28', label: 'money day', subLabel: 'деньги по месяцам', get: () => this.fxSelected.is === 'money', is: 'checkbox', run },
                        ]
                    },
                ]
            },
            {
                id: 'ifShowHide', icon: 'flat-color-icons:search:28', label: 'if - show / hide', subLabel: 'показать / скрыть', expanded: false, items: [
                    { id: 'hideInfo', icon: 'flat-color-icons:about:28', label: 'hide info', subLabel: 'скрыть инфо таб', get: () => this.fxSelected.hideInfo, is: 'checkbox', run },
                    { id: 'ifCalendar', icon: 'flat-color-icons:planner:28', label: 'calendar', subLabel: 'показывать календарь', get: () => this.fxSelected.if?.ifCalendar, is: 'checkbox', run },
                    { id: 'ifEvents', icon: 'flat-color-icons:overtime:28', label: 'events', subLabel: 'показывать события', get: () => this.fxSelected.if?.ifEvents, is: 'checkbox', run },
                ]
            },
            {
                id: 'add-form', icon: 'flat-color-icons:template:28', label: 'form settings', subLabel: 'установки форм', expanded: false, value: 'add form', is: 'button', run, items: [
                    { id: 'ifForms', icon: 'flat-color-icons:template:28', label: 'is form', subLabel: 'показать / скрыть форму', get: () => this.fxSelected.if?.ifForm, is: 'checkbox', run },
                    { id: 'ifConstructor', icon: 'flat-color-icons:services:28', label: 'is constructor', subLabel: 'конструктор форм', get: () => this.fxSelected.if.ifConstructor, is: 'checkbox', run },
                    { id: 'showConstructor', icon: 'flat-color-icons:services:28', label: 'show constructor', subLabel: 'показать конструктор', value: 'show / hide', is: 'button', run },
                ]
            },
            {
                id: 'setsIcon', icon: 'flat-color-icons:stack-of-photos:28', label: 'icon', subLabel: 'действия с иконками', expanded: false, items: [
                    { id: 'iconName', icon: this.fxSelected.icon, label: 'icon name', subLabel: 'имя иконки', get: () => this.fxSelected.icon, _value: 'icon', run },
                    { id: 'selectIcon', icon: 'flat-color-icons:gallery:28', label: 'set icon', value: 'select icon', is: 'button', run },
                    { id: 'sizeIcon', icon: 'flat-color-icons:edit-image:28', label: 'icon size', subLabel: 'размер иконки', get: () => this.fxSelected.iconSize || 24, type: 'number', run },
                    { id: 'deleteIcon', icon: 'fx:close:28', fill: 'red', label: 'delete icon', value: 'delete icon', is: 'button', run },
                ]
            },
            {
                id: 'advancedSet', icon: 'flat-color-icons:graduation-cap:28', label: 'advanced', subLabel: 'расширенные настройки', expanded: false, items: [
                    {
                        id: 'shareItem', icon: 'flat-color-icons:share:28', label: 'share', subLabel: 'поделиться', items: [
                            // { id: 'share-selected', icon: 'flat-color-icons:link:28', label: 'share selected', value: 'selected item', is: 'button', run },
                            { id: 'share-db', icon: 'flat-color-icons:database:28', label: 'share db', subLabel: 'в новой вкладке', value: 'share db', is: 'button', run },
                        ]
                    },
                    {
                        id: 'treeData', icon: 'flat-color-icons:tree-structure:28', label: 'tree data', subLabel: 'данные дерева', items: [
                            { id: 'export-only-tree', icon: 'flat-color-icons:genealogy:28', label: 'export only tree', subLabel: 'экспорт дерева', value: 'export only tree', is: 'button', run },
                            { id: 'export-tree', icon: 'flat-color-icons:export:28', label: 'export tree & data', subLabel: 'экспорт дерева и данных', value: 'export tree', is: 'button', run },
                            { id: 'import-tree', icon: 'flat-color-icons:import:28', label: 'import tree', subLabel: 'импорт дерева', value: 'import tree', is: 'button', run },
                        ]
                    },
                    {
                        id: 'showCode', icon: 'fluent-color:code-block-24:28', label: 'code', subLabel: 'служебные данные', items: [
                            { id: 'showFX', icon: 'vscode-icons:file-type-light-json:28', label: 'fxSelected', value: 'view FX', is: 'button', run },
                            { id: 'showBS', icon: 'vscode-icons:file-type-light-json:28', label: 'bsSelected', value: 'view BS', is: 'button', run },
                        ]
                    },
                ]
            },
        ]
        this.isShowTreeSets = true;
        await FX.showGrid({ type: 'tree-sets', id: this.id + '-tree-sets', item: item(), rowHeight: 32, hideSelected: true }, { id: this.id + '-tree-sets-dd', label: this.shortLabel, parent: e.target, intersect: true, align: 'left', class: 'br', minWidth: 300, draggable: true, resizable: false, btnCloseOnly: true }, true);
        this.isShowTreeSets = false;
        return;
    }
    async showAddItem(_e) {
        const selected = this.fxSelected,
            res = { inside: true };
        const run = async (e, item) => {
            let id = item.id,
                spl = item.icon?.split(':'),
                icon = spl?.[0] + (spl?.[1] ? ':' + spl?.[1] : '');
            if (id === 'sbutton') {
                this.showAddItemSelectButton = item.value;
                // console.log(this.showAddItemSelectButton);
            }
            else if (id === 'add-sbutton') {
                // console.log(this.showAddItemSelectButton);
                if (this.showAddItemSelectButton === 'сын')
                    this.addsBoy(res.inside);
                else if (this.showAddItemSelectButton === 'дочь')
                    this.addsGirl(res.inside);
                else if (this.showAddItemSelectButton === 'супруг(а)')
                    this.addsCouples(res.inside, this.fxSelected);
                else if (this.showAddItemSelectButton === 'отец' || this.showAddItemSelectButton === 'мать') {
                    let type = this.showAddItemSelectButton === 'отец' ? 'man' : this.showAddItemSelectButton === 'мать' ? 'woman' : '';
                    if (type)
                        this.addsParents(res.inside, type);
                }
            }
            else if (id === 'inside') {
                this.showAddItemInside = res.inside = item.value;
            }
            else if (id.startsWith('adds')) {
                this[id](res.inside);
            }
            else {
                res.value = item.id;
                res.icon = icon;
                res.noAddInfo = true;
                let persona = this.on_addItem('', res);
            }
        }
        const item = this.dataAddItem = () => [
            { id: 'inside', icon: 'fc:parallel-tasks:28', label: 'add inside', subLabel: 'добавлять внутрь', get: () => this.showAddItemInside, is: 'checkbox', run },
            { id: 'man', icon: 'flat-color-icons:businessman:28', label: 'man', subLabel: 'добавить мужчину', value: 'мужчина', is: 'button', run },
            { id: 'woman', icon: 'flat-color-icons:businesswoman:28', label: 'woman', subLabel: 'добавить женщину', value: 'женщина', is: 'button', run },
            { id: 'addsParents', icon: 'fx:dots:28', svg: $icons.parents, label: 'parents', subLabel: 'добавить родителей', value: 'родители', is: 'button', run },
            { id: 'addsCouples', icon: 'fx:dots:28', svg: $icons.weddingCouple, label: 'couples', subLabel: 'семейную пару', value: 'семейная пара', is: 'button', run },
            { id: 'add-sbutton', icon: 'bubbles:family-child-outline:28', fill: 'violet', label: 'add selected', subLabel: 'сын, дочь, муж, жена ...', get: () => this.showAddItemSelectButton || '', is: 'sbutton', options: ['', 'сын', 'дочь', 'супруг(а)', 'отец', 'мать'], run },
            { id: 'calendar', icon: 'flat-color-icons:planner:28', label: 'calendar', subLabel: 'добавить календарь', value: 'календарь', is: 'button', run },
            { id: 'info', icon: 'flat-color-icons:multiple-smartphones:28', label: 'info link', subLabel: 'добавить инфо', value: 'инфо', is: 'button', run },
            { id: 'info', icon: '', label: 'info link (no icon)', subLabel: 'добавить инфо без иконки', value: 'инфо', is: 'button', run },
        ]
        this.isShowAddItem = true;
        this.showAddItemInside = true;
        this.showAddItemSelectButton ||= '';
        await FX.showGrid({ type: 'add-item', id: this.id + '-add-item', item: item(), rowHeight: 32, hideSelected: true }, { id: this.id + '-add-item-dd', label: this.shortLabel, intersect: true, align: 'left', class: 'br', minWidth: 300, draggable: true, resizable: false, btnCloseOnly: true }, true);
        this.isShowAddItem = false;
    }

    async addsParents(inside, type) {
        let selected = this.fxSelected;
        if (selected.is === 'man' || selected.is === 'woman') {
            let man = { inside };
            let woman = { inside };
            if (!type || type === 'man') {
                man.value = 'man';
                man.icon = 'flat-color-icons:businessman';
                man.noAddInfo = true;
                man = await this.on_addItem('', man);
                man.label = 'Отец';
                selected.father = man._id;
                man.spouses ||= {};
                if (type && selected.mother) {
                    man.spouses[selected.mother] = { _id: selected.mother };
                    let mother = this.fxFlat[selected.mother];
                    mother.spouses ||= {};
                    mother.spouses[man._id] = { _id: man._id };
                }
            }
            if (!type || type === 'woman') {
                woman.value = 'woman';
                woman.icon = 'flat-color-icons:businesswoman';
                woman.noAddInfo = true;
                woman = await this.on_addItem('', woman);
                woman.label = 'Мать';
                selected.mother = woman._id;
                woman.spouses ||= {};
                if (type && selected.father) {
                    woman.spouses[selected.father] = { _id: selected.father };
                    let father = this.fxFlat[selected.father];
                    father.spouses ||= {};
                    father.spouses[woman._id] = { _id: woman._id };
                }
            }
            if (!type) {
                man.spouses[woman._id] = { _id: woman._id };
                woman.spouses[man._id] = { _id: man._id };
            }
        }
    }
    async addsCouples(inside, selected) {
        if (!selected || selected.is === 'man' || selected.is === 'woman') {
            let man = { inside };
            let woman = { inside };
            if (selected?.is === 'man') {
                man = selected;
            } else {
                man.value = 'man';
                man.icon = 'flat-color-icons:businessman';
                man.noAddInfo = true;
                man = await this.on_addItem('', man);
                man.label = 'Муж';
            }
            if (selected?.is === 'woman') {
                woman = selected;
            } else {
                woman.value = 'woman';
                woman.icon = 'flat-color-icons:businesswoman';
                woman.noAddInfo = true;
                woman = await this.on_addItem('', woman);
                woman.label = 'Жена';
            }
            man.spouses ||= {};
            man.spouses[woman._id] = { _id: woman._id };
            woman.spouses ||= {};
            woman.spouses[man._id] = { _id: man._id };
        }
    }
    async addsBoy(inside) {
        if (this.fxSelected.is === 'man' || this.fxSelected.is === 'woman') {
            let boy = { inside };
            boy.value = 'man';
            boy.icon = 'flat-color-icons:businessman';
            boy.noAddInfo = true;
            boy = await this.on_addItem('', boy);
            boy.label = 'Сын';
            if (this.fxSelected.is === 'man') {
                boy.father = this.fxSelected._id;
            } else {
                boy.mother = this.fxSelected._id;
            }
        }
    }
    async addsGirl(inside) {
        if (this.fxSelected.is !== 'man' || this.fxSelected.is !== 'woman') {
            let girl = { inside };
            girl.value = 'woman';
            girl.icon = 'flat-color-icons:businesswoman';
            girl.noAddInfo = true;
            girl = await this.on_addItem('', girl);
            girl.label = 'Дочь';
            if (this.fxSelected.is === 'man') {
                girl.father = this.fxSelected._id;
            } else {
                girl.mother = this.fxSelected._id;
            }
        }
    }

    async getLZS(lzs) {
        lzs = FX.jduri(lzs);
        lzs = JSON.parse(lzs);
        if (!lzs?.name) return;
        this.hideDBS = lzs.hideDBS;
        let name = lzs.name,
            dbUrl = lzs.dbUrl;
        this.dbUrl = dbUrl;
        this.$update();
        let dbs = await FX.purix(name, dbUrl);
        this.dbLocal = dbs.local;
        this.dbRemote = dbs.remote;

        await this.dbLocal.replicate.from(this.dbRemote).on('complete', result => {
            // console.log(result);
            this.async(() => this.showLoader = false, 100);
            this.syncHandler = this.dbLocal.sync(this.dbRemote, { live: true, retry: true })
                .on('change', change => { /*console.log('change')*/ })
                .on('paused', paused => { /*console.log('paused')*/ })
                .on('error', error => { /*console.log('sync error')*/ });
            this.on_dbSelected();
            this.restoreState(lzs);
        }).on('error', error => { console.log('replicate error - ', error) });

    }
    restoreState(lzs) {
        this.async(() => {
            this._updateSaves();
            if (this.lastLZS === lzs.lastLZS) return;
            this.lastLZS = lzs.lastLZS;
            Object.keys(lzs.main || {}).forEach(key => this.main[key] = lzs.main[key]);
            this.main._updateSaves();
            this.async(() => {
                this.main.fxTabs.selectTab(lzs.tabs?.activeTab);
                Object.keys(lzs.tree || {}).forEach(key => this.tree[key] = lzs.tree[key]);
                this.tree.restoreTreeState();
                this.$update();
            }, 100)
        }, 100)
    }

    async exportOnlyTree() {
        try {
            if (!this.tree || !this.tree.item) {
                await FX.showModal({
                    ok: 'Ok',
                    modal: '300, 160',
                    label: 'Error',
                    info1: 'No tree data available',
                    info2: 'Нет данных дерева для экспорта'
                })
                return;
            }
            this.tree.storeTreeState();
            const exportData = {
                type: 'fxTree',
                exportDate: new Date().toISOString(),
                database: this.dbLocal?.name || 'unknown',
                treeData: this.tree.item,
                treeState: {
                    selectedID: this.tree.selectedID,
                    rootID: this.tree.rootID,
                    expanded: this.tree.expanded,
                    plainTextMode: this.tree.plainTextMode
                }
            }
            const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `fxTree_${this.dbLocal?.name || 'tree'}_${FX.dates().short}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            await FX.showModal({
                ok: 'Ok',
                modal: '300, 160',
                label: 'Success',
                info1: 'Tree exported successfully',
                info2: 'Дерево успешно экспортировано'
            })
        } catch (error) {
            console.error('Export tree error:', error);
            await FX.showModal({
                ok: 'Ok',
                modal: '300, 160',
                label: 'Error',
                info1: 'Export failed',
                info2: 'Ошибка экспорта: ' + error.message
            })
        }
    }

    async exportTree() {
        try {
            if (!this.tree || !this.tree.item) {
                await FX.showModal({
                    ok: 'Ok',
                    modal: '300, 160',
                    label: 'Error',
                    info1: 'No tree data available',
                    info2: 'Нет данных дерева для экспорта'
                })
                return;
            }
            const checkedItems = this.tree.getCheckedItems();
            const hasCheckedItems = checkedItems.length > 0;
            let exportOnlyChecked = false;
            if (hasCheckedItems) {
                const result = await FX.showModal({
                    ok: 'Все узлы',
                    widthOk: 80,
                    cancel: 'Только отмеченные',
                    widthCancel: 180,
                    modal: '360, 150',
                    label: 'Экспорт дерева',
                    info1: `Найдено ${checkedItems.length} отмеченных узлов`,
                    info2: 'Экспортировать все узлы или только отмеченные?'
                })
                if (result.detail === 'close') return;
                exportOnlyChecked = result.detail !== 'ok';
            }
            this.tree.storeTreeState();
            let treeData;
            if (exportOnlyChecked && hasCheckedItems) {
                treeData = this.tree.getCheckedItems();
                treeData.forEach(i => {
                    delete i.checked;
                    delete i.expanded;
                    delete i.items;
                    delete i.isReady;
                    delete i._rev;
                })
                treeData = { items: treeData };
            } else {
                treeData = this.tree.item;
            }
            let exportedItemIds = BS_UTILS.allItems(treeData).map(i => i._id);
            const additionalData = await this.collectAdditionalData(exportedItemIds);
            let attachmentCount = 0;
            Object.values(additionalData).forEach(doc => {
                if (doc._attachments) {
                    attachmentCount += Object.keys(doc._attachments).length;
                }
            })
            const exportData = {
                type: 'fxTree',
                exportDate: new Date().toISOString(),
                database: this.dbLocal?.name || 'unknown',
                exportMode: exportOnlyChecked ? 'checked' : 'all',
                itemCount: exportedItemIds.length,
                additionalDataCount: Object.keys(additionalData).length,
                attachmentCount: attachmentCount,
                treeData: treeData,
                additionalData: additionalData,
                treeState: {
                    selectedID: this.tree.selectedID,
                    rootID: this.tree.rootID,
                    expanded: this.tree.expanded,
                    plainTextMode: this.tree.plainTextMode
                }
            }
            const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            const suffix = exportOnlyChecked ? '_checked' : '';
            a.href = url;
            a.download = `fxTree_${this.dbLocal?.name || 'tree'}${suffix}_${FX.dates().short}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            await FX.showModal({
                ok: 'Ok',
                modal: '400, 200',
                label: 'Success',
                info1: `Экспорт завершен: ${exportData.itemCount} узлов, ${exportData.additionalDataCount} доп. данных, ${exportData.attachmentCount} вложений`,
                info2: `Дерево успешно экспортировано (${exportOnlyChecked ? 'только отмеченные' : 'все узлы'})`
            })
        } catch (error) {
            console.error('Export tree error:', error);
            await FX.showModal({
                ok: 'Ok',
                modal: '300, 160',
                label: 'Error',
                info1: 'Export failed',
                info2: 'Ошибка экспорта: ' + error.message
            })
        }
    }
    async collectAdditionalData(itemIds) {
        const additionalData = {};
        const prefixes = ['info', 'events', 'hlsDay', 'money', 'moneyTotal', 'fields', 'file'];
        for (const prefix of prefixes) {
            const startkey = `${prefix}:`;
            const endkey = `${prefix}:\ufff0`;
            try {
                const allDocs = await this.dbLocal.allDocs({
                    include_docs: true,
                    attachments: true,
                    startkey,
                    endkey
                })
                allDocs.rows.forEach(row => {
                    const doc = row.doc;
                    const relatedItemId = doc.parentId || doc._id.split(':').slice(1).join(':');
                    if (itemIds.includes(relatedItemId) || itemIds.some(id => doc._id.includes(id))) {
                        if (doc._attachments) {
                            const attachments = {};
                            for (const [attachmentId, attachment] of Object.entries(doc._attachments)) {
                                attachments[attachmentId] = {
                                    content_type: attachment.content_type,
                                    data: attachment.data
                                }
                            }
                            doc._attachments = attachments;
                        }
                        additionalData[doc._id] = doc;
                    }
                })
            } catch (error) {
                console.warn(`Error collecting ${prefix} data:`, error);
            }
        }
        return additionalData;
    }

    async importTree() {
        try {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';
            input.onchange = async (e) => {
                const file = e.target.files[0];
                if (!file) return;
                try {
                    const text = await file.text();
                    const importData = JSON.parse(text);
                    if (importData.type !== 'fxTree' || !importData.treeData) {
                        await FX.showModal({
                            ok: 'Ok',
                            modal: '300, 160',
                            label: 'Error',
                            info1: 'Invalid tree file format',
                            info2: 'Неверный формат файла дерева'
                        })
                        return;
                    }
                    const result = await FX.showModal({
                        ok: 'Import',
                        cancel: 'Cancel',
                        modal: '400, 200',
                        label: 'Import Tree',
                        info1: 'Import new data?',
                        info2: 'Импортировать новые данные дерева. Продолжить?'
                    })
                    if (result.detail !== 'ok') return;
                    await this.importTreeReplace(importData);
                } catch (error) {
                    console.error('Import tree error:', error);
                    await FX.showModal({
                        ok: 'Ok',
                        modal: '300, 160',
                        label: 'Error',
                        info1: 'Import failed',
                        info2: 'Ошибка импорта: ' + error.message
                    })
                }
            }
            input.click();
        } catch (error) {
            console.error('Import tree error:', error);
            await FX.showModal({
                ok: 'Ok',
                modal: '300, 160',
                label: 'Error',
                info1: 'Import failed',
                info2: 'Ошибка импорта: ' + error.message
            })
        }
    }
    async importTreeReplace(importData) {
        const all = BS_UTILS.allItems(importData.treeData, false, true);
        this.bsFlatItems ||= {};
        all.forEach(i => {
            if (i._id || i.doc._id) {
                const doc = { ...i.doc || i };
                delete doc.items;
                delete doc.isReady;
                delete doc.expanded;
                delete doc.checked;
                delete doc._rev;
                const item = new BS_ITEM({ _id: doc._id, type: 'item', isNew: true, doc });
                this.bsFlatItems[doc._id] = item;
            }
        })
        if (importData.additionalData) {
            await this.importAdditionalData(importData.additionalData);
        }
        this.fxItems = await this.buildTree();
        this.$update();
        const stats = this.getImportStats(importData);
        await FX.showModal({
            ok: 'Ok',
            modal: '400, 200',
            label: 'Success',
            info1: `Импорт завершен: ${stats.itemCount} узлов, ${stats.additionalDataCount} доп. данных, ${stats.attachmentCount} вложений`,
            info2: 'Дерево успешно импортировано'
        })
    }
    async importAdditionalData(additionalData) {
        this.bsFlatDrafts ||= {};
        for (const [docId, doc] of Object.entries(additionalData)) {
            const cleanDoc = { ...doc };
            delete cleanDoc._rev;
            if (cleanDoc._attachments) {
                await this.importAttachments(docId, cleanDoc._attachments);
            }
            const type = docId.split(':')[0];
            const item = new BS_ITEM({ _id: docId, type, isNew: true, doc: cleanDoc });
            this.bsFlatDrafts[docId] = item;
        }
    }
    getImportStats(importData) {
        const itemCount = importData.itemCount || BS_UTILS.allItems(importData.treeData).length;
        const additionalDataCount = importData.additionalDataCount || Object.keys(importData.additionalData || {}).length;
        let attachmentCount = importData.attachmentCount || 0;
        if (!importData.attachmentCount && importData.additionalData) {
            Object.values(importData.additionalData).forEach(doc => {
                if (doc._attachments) {
                    attachmentCount += Object.keys(doc._attachments).length;
                }
            })
        }
        return { itemCount, additionalDataCount, attachmentCount };
    }
    async importAttachments(docId, attachments) {
        try {
            let existingDoc;
            try {
                existingDoc = await this.dbLocal.get(docId);
            } catch (error) {
                existingDoc = {
                    _id: docId,
                    type: docId.split(':')[0],
                    imported: true
                }
                await this.dbLocal.put(existingDoc);
                existingDoc = await this.dbLocal.get(docId);
            }
            for (const [attachmentId, attachment] of Object.entries(attachments)) {
                try {
                    let blob;
                    if (typeof attachment.data === 'string') {
                        const binaryString = atob(attachment.data);
                        const bytes = new Uint8Array(binaryString.length);
                        for (let i = 0; i < binaryString.length; i++) {
                            bytes[i] = binaryString.charCodeAt(i);
                        }
                        blob = new Blob([bytes], { type: attachment.content_type });
                    } else {
                        blob = attachment.data;
                    }
                    await this.dbLocal.putAttachment(
                        docId,
                        attachmentId,
                        existingDoc._rev,
                        blob,
                        attachment.content_type
                    );
                    existingDoc = await this.dbLocal.get(docId);
                } catch (error) {
                    console.warn(`Failed to import attachment ${attachmentId} for document ${docId}:`, error);
                }
            }
        } catch (error) {
            console.warn(`Failed to import attachments for document ${docId}:`, error);
        }
    }
}
customElements.define('fx-base', FxBase);
